#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
test_superior_performance.py

Comprehensive evaluation of the superior decentralized model against baselines.
Tests for:
- Makespan performance vs EDF/Tercio
- Workload balance performance vs baselines
- Success rate and convergence
"""

import os
import sys
import time
import argparse
import numpy as np
import pandas as pd
import torch
import torch.nn.functional as F
from collections import defaultdict

# Import required modules
from utils import build_hetgraph, hetgraph_node_helper
from hetnet import MultiRobotDecentralizedSystem
from baselines import solve_edf, solve_tercio
from multi_objective_utils import calculate_workload_balance
import copy


def load_superior_model(checkpoint_path, num_robots, device):
    """Load the superior trained model."""
    # Enhanced network architecture (same as training)
    in_dim = {'task': 6, 'loc': 1, 'robot': 1, 'state': 4, 'value': 1}
    hid_dim = {'task': 128, 'loc': 128, 'robot': 128, 'state': 128, 'value': 128}
    out_dim = {'task': 64, 'loc': 64, 'robot': 64, 'state': 64, 'value': 1}
    
    cetypes = [
        ('task', 'temporal', 'task'), ('task', 'located_in', 'loc'), 
        ('loc', 'near', 'loc'), ('task', 'assigned_to', 'robot'), 
        ('robot', 'com', 'robot'), ('task', 'tin', 'state'), 
        ('loc', 'lin', 'state'), ('robot', 'rin', 'state'), 
        ('state', 'sin', 'state'), ('task', 'tto', 'value'), 
        ('robot', 'rto', 'value'), ('state', 'sto', 'value'), 
        ('value', 'vto', 'value'), ('task', 'take_time', 'robot'), 
        ('robot', 'use_time', 'task')
    ]
    
    system = MultiRobotDecentralizedSystem(in_dim, hid_dim, out_dim, cetypes, num_robots, 10).to(device)
    
    if os.path.exists(checkpoint_path):
        checkpoint = torch.load(checkpoint_path, map_location=device)
        
        # Load robot networks
        for i in range(num_robots):
            robot_key = f'robot_{i}'
            if robot_key in checkpoint['robot_networks']:
                system.get_robot_network(i).load_state_dict(checkpoint['robot_networks'][robot_key])
        
        print(f"✅ Loaded superior model from {checkpoint_path}")
        return system
    else:
        print(f"❌ Checkpoint not found: {checkpoint_path}")
        return None


def solve_with_superior_model(system, env, num_robots, device, max_steps=50):
    """
    Solve using the superior decentralized model with enhanced decision making.
    """
    system.eval()
    
    with torch.no_grad():
        for step in range(max_steps):
            unscheduled = [tid for tid in range(1, env.num_tasks+1) if tid not in env.partialw]
            if not unscheduled:
                break
            
            # Enhanced decision making for each robot
            robot_decisions = []
            
            for robot_id in range(num_robots):
                try:
                    # Build graph and features
                    g = build_hetgraph(
                        env.g, env.num_tasks, num_robots, env.durations.astype(np.float32),
                        6, np.array(env.locations, dtype=np.int64), 1, env.partials,
                        np.array(unscheduled, dtype=np.int32), robot_id, np.array(unscheduled, dtype=np.int32)
                    ).to(device)
                    
                    feat = hetgraph_node_helper(
                        env.g.number_of_nodes(), env.partialw, env.partials,
                        env.locations, env.durations, 6, num_robots, len(unscheduled)
                    )
                    feat = {k: torch.tensor(v, device=device, dtype=torch.float32) for k, v in feat.items()}
                    
                    # Enhanced forward pass with more communication
                    out = system.forward_with_communication(robot_id, g, feat, communication_rounds=10)
                    q_values = out['value'].squeeze()
                    confidence = out['confidence'].squeeze()
                    
                    if len(q_values.shape) == 0:
                        q_values = q_values.unsqueeze(0)
                        confidence = confidence.unsqueeze(0)
                    
                    # Enhanced decision making with confidence weighting
                    probs = F.softmax(q_values, dim=0)
                    confidence_weights = F.softmax(confidence, dim=0)
                    
                    # Combine Q-values and confidence for better decisions
                    combined_scores = 0.7 * probs + 0.3 * confidence_weights
                    
                    # Select best task for this robot
                    best_idx = torch.argmax(combined_scores).item()
                    if best_idx < len(unscheduled):
                        task_id = unscheduled[best_idx]
                        score = combined_scores[best_idx].item()
                        robot_decisions.append((robot_id, task_id, score))
                        
                except Exception as e:
                    print(f"⚠️  Error in robot {robot_id} decision: {e}")
                    continue
            
            # Select best overall decision
            if robot_decisions:
                # Sort by score and select best
                robot_decisions.sort(key=lambda x: x[2], reverse=True)
                best_robot, best_task, best_score = robot_decisions[0]
                
                # Insert the task
                success, reward, done = env.insert_robot(best_task, best_robot)
                if not success:
                    print(f"⚠️  Failed to insert task {best_task} to robot {best_robot}")
                    break
            else:
                print("⚠️  No valid decisions from any robot")
                break
    
    return env.partialw, env.partials


def evaluate_superior_model(checkpoint_path, test_instances, num_robots, device):
    """
    Comprehensive evaluation of the superior model against baselines.
    """
    print(f"🔍 Evaluating SUPERIOR model against baselines...")
    
    # Load superior model
    system = load_superior_model(checkpoint_path, num_robots, device)
    if system is None:
        return None
    
    results = {
        'superior': {'makespans': [], 'balances': [], 'success_count': 0},
        'edf': {'makespans': [], 'balances': [], 'success_count': 0},
        'tercio': {'makespans': [], 'balances': [], 'success_count': 0}
    }
    
    print(f"📊 Testing on {len(test_instances)} instances...")
    
    for i, instance_path in enumerate(test_instances):
        print(f"\n🔄 Testing instance {i+1}/{len(test_instances)}: {os.path.basename(instance_path)}")
        
        try:
            # Load problem instance
            from utils import load_problem_instance
            env = load_problem_instance(instance_path, num_robots)
            
            # Test Superior Model
            env_copy = copy.deepcopy(env)
            start_time = time.time()
            partialw, partials = solve_with_superior_model(system, env_copy, num_robots, device)
            superior_time = time.time() - start_time
            
            if len(partialw) == env.num_tasks:
                makespan = max([max(schedule) if schedule else 0 for schedule in partials])
                balance = calculate_workload_balance(partials)
                results['superior']['makespans'].append(makespan)
                results['superior']['balances'].append(balance)
                results['superior']['success_count'] += 1
                print(f"  ✅ Superior: makespan={makespan:.1f}, balance={balance:.3f}, time={superior_time:.2f}s")
            else:
                print(f"  ❌ Superior: Failed to schedule all tasks ({len(partialw)}/{env.num_tasks})")
            
            # Test EDF Baseline
            env_copy = copy.deepcopy(env)
            start_time = time.time()
            edf_partials = solve_edf(env_copy, num_robots)
            edf_time = time.time() - start_time
            
            if edf_partials and all(len(schedule) > 0 for schedule in edf_partials):
                makespan = max([max(schedule) if schedule else 0 for schedule in edf_partials])
                balance = calculate_workload_balance(edf_partials)
                results['edf']['makespans'].append(makespan)
                results['edf']['balances'].append(balance)
                results['edf']['success_count'] += 1
                print(f"  📊 EDF: makespan={makespan:.1f}, balance={balance:.3f}, time={edf_time:.2f}s")
            else:
                print(f"  ❌ EDF: Failed to solve")
            
            # Test Tercio Baseline
            env_copy = copy.deepcopy(env)
            start_time = time.time()
            tercio_partials = solve_tercio(env_copy, num_robots)
            tercio_time = time.time() - start_time
            
            if tercio_partials and all(len(schedule) > 0 for schedule in tercio_partials):
                makespan = max([max(schedule) if schedule else 0 for schedule in tercio_partials])
                balance = calculate_workload_balance(tercio_partials)
                results['tercio']['makespans'].append(makespan)
                results['tercio']['balances'].append(balance)
                results['tercio']['success_count'] += 1
                print(f"  📊 Tercio: makespan={makespan:.1f}, balance={balance:.3f}, time={tercio_time:.2f}s")
            else:
                print(f"  ❌ Tercio: Failed to solve")
                
        except Exception as e:
            print(f"  ❌ Error processing instance: {e}")
            continue
    
    return results


def analyze_results(results):
    """Analyze and compare results."""
    print(f"\n🎯 COMPREHENSIVE PERFORMANCE ANALYSIS")
    print(f"=" * 60)
    
    for method in ['superior', 'edf', 'tercio']:
        if results[method]['makespans']:
            avg_makespan = np.mean(results[method]['makespans'])
            avg_balance = np.mean(results[method]['balances'])
            success_rate = results[method]['success_count'] / len(results[method]['makespans']) * 100
            
            print(f"\n📊 {method.upper()} Results:")
            print(f"  Success Rate: {success_rate:.1f}%")
            print(f"  Avg Makespan: {avg_makespan:.2f}")
            print(f"  Avg Balance:  {avg_balance:.3f}")
            print(f"  Makespan Std: {np.std(results[method]['makespans']):.2f}")
            print(f"  Balance Std:  {np.std(results[method]['balances']):.3f}")
    
    # Performance comparison
    if results['superior']['makespans'] and results['edf']['makespans']:
        superior_makespan = np.mean(results['superior']['makespans'])
        edf_makespan = np.mean(results['edf']['makespans'])
        makespan_improvement = (edf_makespan - superior_makespan) / edf_makespan * 100
        
        superior_balance = np.mean(results['superior']['balances'])
        edf_balance = np.mean(results['edf']['balances'])
        balance_improvement = (edf_balance - superior_balance) / edf_balance * 100
        
        print(f"\n🏆 SUPERIOR vs EDF:")
        print(f"  Makespan Improvement: {makespan_improvement:+.1f}%")
        print(f"  Balance Improvement:  {balance_improvement:+.1f}%")
    
    if results['superior']['makespans'] and results['tercio']['makespans']:
        superior_makespan = np.mean(results['superior']['makespans'])
        tercio_makespan = np.mean(results['tercio']['makespans'])
        makespan_improvement = (tercio_makespan - superior_makespan) / tercio_makespan * 100
        
        superior_balance = np.mean(results['superior']['balances'])
        tercio_balance = np.mean(results['tercio']['balances'])
        balance_improvement = (tercio_balance - superior_balance) / tercio_balance * 100
        
        print(f"\n🏆 SUPERIOR vs TERCIO:")
        print(f"  Makespan Improvement: {makespan_improvement:+.1f}%")
        print(f"  Balance Improvement:  {balance_improvement:+.1f}%")


def main():
    parser = argparse.ArgumentParser(description="Test Superior Decentralized Model")
    parser.add_argument("--checkpoint", required=True, help="Path to superior model checkpoint")
    parser.add_argument("--test-path", default="./problem_instances/constraints")
    parser.add_argument("--test-start", type=int, default=1)
    parser.add_argument("--test-end", type=int, default=50)
    parser.add_argument("--num-robots", type=int, default=2)
    parser.add_argument("--device", default="cpu")
    
    args = parser.parse_args()
    
    # Get test instances
    test_instances = []
    for i in range(args.test_start, args.test_end + 1):
        instance_path = os.path.join(args.test_path, f"{i:05d}.txt")
        if os.path.exists(instance_path):
            test_instances.append(instance_path)
    
    if not test_instances:
        print("❌ No test instances found!")
        return
    
    print(f"🚀 Testing SUPERIOR model performance")
    print(f"📁 Checkpoint: {args.checkpoint}")
    print(f"📊 Test instances: {len(test_instances)}")
    
    # Evaluate model
    device = torch.device(args.device)
    results = evaluate_superior_model(args.checkpoint, test_instances, args.num_robots, device)
    
    if results:
        analyze_results(results)
        
        # Save results
        os.makedirs("./results_superior", exist_ok=True)
        
        for method in ['superior', 'edf', 'tercio']:
            if results[method]['makespans']:
                df = pd.DataFrame({
                    'makespan': results[method]['makespans'],
                    'balance': results[method]['balances']
                })
                df.to_csv(f"./results_superior/{method}_results.csv", index=False)
                print(f"💾 Saved {method} results to ./results_superior/{method}_results.csv")
        
        print(f"\n🎉 SUPERIOR model evaluation completed!")
    else:
        print(f"❌ Evaluation failed!")


if __name__ == "__main__":
    main()

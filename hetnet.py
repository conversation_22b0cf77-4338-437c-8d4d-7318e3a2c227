import torch
import torch.nn as nn

from graph.hetgat import HeteroGATLayer

# input similar to HeteroGATLayer
# merge = 'cat' or 'avg'
class MultiHeteroGATLayer(nn.Module):
    def __init__(self, in_dim, out_dim, cetypes,
                 num_heads, merge='cat'):
        super(MultiHeteroGATLayer, self).__init__()
        
        self.num_heads = num_heads
        self.merge = merge
        
        self.heads = nn.ModuleList()
        
        if self.merge == 'cat':        
            for i in range(self.num_heads):
                self.heads.append(HeteroGATLayer(in_dim, out_dim, cetypes))
        else:
            #self.relu = nn.ReLU()
            for i in range(self.num_heads):
                self.heads.append(HeteroGATLayer(in_dim, out_dim, cetypes,
                                                 use_relu = False))            

    def forward(self, g, feat_dict):
        tmp = {}
        for ntype in feat_dict:
            tmp[ntype] = []
            
        for i in range(self.num_heads):
            head_out = self.heads[i](g, feat_dict)
            
            for ntype in feat_dict:
                tmp[ntype].append(head_out[ntype])
        
        results = {}
        if self.merge == 'cat':
            # concat on the output feature dimension (dim=1)  
            for ntype in feat_dict:
                results[ntype] = torch.cat(tmp[ntype], dim=1)
        else:
            # merge using average
            for ntype in feat_dict:
                # dont use relu as the predicted q scores are negative values
                #results[ntype] = self.relu(torch.mean(torch.stack(tmp[ntype]), dim=0))
                results[ntype] = torch.mean(torch.stack(tmp[ntype]), dim=0)
        
        return results

class ScheduleNet4Layer(nn.Module):
    def __init__(self, in_dim, hid_dim, out_dim, cetypes, num_heads=4):
        super(ScheduleNet4Layer, self).__init__()

        hid_dim_input = {}
        for key in hid_dim:
            hid_dim_input[key] = hid_dim[key] * num_heads

        self.layer1 = MultiHeteroGATLayer(in_dim, hid_dim, cetypes, num_heads)
        self.layer2 = MultiHeteroGATLayer(hid_dim_input, hid_dim, cetypes,
                                          num_heads)
        self.layer3 = MultiHeteroGATLayer(hid_dim_input, hid_dim, cetypes,
                                          num_heads)
        self.layer4 = MultiHeteroGATLayer(hid_dim_input, out_dim, cetypes,
                                          num_heads, merge='avg')

        # FIXED: Add dropout for regularization
        self.dropout = nn.Dropout(0.2)

    '''
    input
        g: DGL heterograph
            number of Q score nodes = number of available actions
        feat_dict: dictionary of input features
    '''
    def forward(self, g, feat_dict):
        h1 = self.layer1(g, feat_dict)
        h1 = {ntype: self.dropout(h1[ntype]) for ntype in h1}

        h2 = self.layer2(g, h1)
        h2 = {ntype: self.dropout(h2[ntype]) for ntype in h2}

        h3 = self.layer3(g, h2)
        h3 = {ntype: self.dropout(h3[ntype]) for ntype in h3}

        h4 = self.layer4(g, h3)
        # No dropout on final layer to preserve output quality

        return h4


class DecentralizedScheduleNet(nn.Module):
    """
    Decentralized version of ScheduleNet4Layer.
    Each robot has its own local network that makes decisions based on limited information.
    """
    def __init__(self, in_dim, hid_dim, out_dim, cetypes, num_heads=4, robot_id=0, num_robots=2):
        super(DecentralizedScheduleNet, self).__init__()

        self.robot_id = robot_id
        self.num_robots = num_robots

        # IMPROVED: Larger network for better expressiveness while maintaining efficiency
        local_hid_dim = {k: max(32, int(v * 0.75)) for k, v in hid_dim.items()}

        hid_dim_input = {}
        for key in local_hid_dim:
            hid_dim_input[key] = local_hid_dim[key] * num_heads

        # IMPROVED: Enhanced architecture with more layers for better representation
        self.layer1 = MultiHeteroGATLayer(in_dim, local_hid_dim, cetypes, num_heads)
        self.layer2 = MultiHeteroGATLayer(hid_dim_input, local_hid_dim, cetypes, num_heads)
        self.layer3 = MultiHeteroGATLayer(hid_dim_input, local_hid_dim, cetypes, num_heads)
        self.layer4 = MultiHeteroGATLayer(hid_dim_input, out_dim, cetypes, num_heads, merge='avg')

        # FIXED: Add dropout for regularization
        self.dropout = nn.Dropout(0.2)

        # IMPROVED: Enhanced decentralized features with better architecture
        self.local_preference_head = nn.Sequential(
            nn.Linear(out_dim['value'], out_dim['value'] // 2),
            nn.ReLU(),
            nn.Linear(out_dim['value'] // 2, 1)
        )
        self.confidence_head = nn.Sequential(
            nn.Linear(out_dim['value'], out_dim['value'] // 2),
            nn.ReLU(),
            nn.Linear(out_dim['value'] // 2, 1),
            nn.Sigmoid()  # Ensure confidence is in [0,1]
        )
        self.communication_head = nn.Sequential(
            nn.Linear(out_dim['value'], out_dim['value']),
            nn.ReLU(),
            nn.Linear(out_dim['value'], out_dim['value'])
        )

        # Communication buffer for neighbor information
        self.neighbor_messages = {}
        self.communication_weight = 0.3  # Weight for neighbor influence

    def set_neighbor_messages(self, neighbor_messages):
        """Set messages received from neighboring robots"""
        self.neighbor_messages = neighbor_messages

    def get_communication_message(self, g, feat_dict):
        """Generate message to send to neighboring robots"""
        with torch.no_grad():
            h1 = self.layer1(g, feat_dict)
            h2 = self.layer2(g, h1)
            h3 = self.layer3(g, h2)

            # Generate communication message
            comm_message = self.communication_head(h3['value'])
            return comm_message.detach()

    def forward(self, g, feat_dict, use_communication=True):
        h1 = self.layer1(g, feat_dict)
        # FIXED: Apply dropout after each GAT layer
        h1 = {ntype: self.dropout(h1[ntype]) for ntype in h1}

        h2 = self.layer2(g, h1)
        h2 = {ntype: self.dropout(h2[ntype]) for ntype in h2}

        h3 = self.layer3(g, h2)
        h3 = {ntype: self.dropout(h3[ntype]) for ntype in h3}

        # IMPROVED: Add fourth layer for better representation
        h4 = self.layer4(g, h3)
        h4 = {ntype: self.dropout(h4[ntype]) for ntype in h4}

        # IMPROVED: Use enhanced representation for decision making
        local_values = h4['value']
        local_preference = self.local_preference_head(local_values)
        confidence = self.confidence_head(local_values)  # Sigmoid already in head

        # Incorporate neighbor information if available
        if use_communication and self.neighbor_messages:
            neighbor_influence = torch.zeros_like(local_values)
            num_neighbors = 0

            for neighbor_id, message in self.neighbor_messages.items():
                if message is not None and message.shape == local_values.shape:
                    neighbor_influence += message
                    num_neighbors += 1

            if num_neighbors > 0:
                neighbor_influence /= num_neighbors

                # Combine local and neighbor information
                combined_values = (1 - self.communication_weight) * local_values + \
                                self.communication_weight * neighbor_influence

                # Adjust confidence based on agreement with neighbors
                agreement = torch.cosine_similarity(local_values, neighbor_influence, dim=1, eps=1e-8)
                confidence = confidence * (0.5 + 0.5 * agreement.unsqueeze(1))
            else:
                combined_values = local_values
        else:
            combined_values = local_values

        # IMPROVED: Return enhanced output with better features
        result = h4.copy()
        result['value'] = combined_values
        result['local_preference'] = local_preference
        result['confidence'] = confidence
        result['robot_id'] = torch.tensor([self.robot_id], dtype=torch.float32, device=local_values.device)

        return result


class MultiRobotDecentralizedSystem(nn.Module):
    """
    System managing multiple decentralized robot networks.
    Handles communication and coordination between robots.
    """
    def __init__(self, in_dim, hid_dim, out_dim, cetypes, num_robots, num_heads=4):
        super(MultiRobotDecentralizedSystem, self).__init__()

        self.num_robots = num_robots
        self.robot_networks = nn.ModuleList()

        # Create individual networks for each robot
        for robot_id in range(num_robots):
            robot_net = DecentralizedScheduleNet(
                in_dim, hid_dim, out_dim, cetypes, num_heads, robot_id, num_robots
            )
            self.robot_networks.append(robot_net)

        # Communication topology (can be modified)
        self.communication_graph = self._build_communication_graph()

        # FIXED: Add dropout for regularization
        self.dropout = nn.Dropout(0.2)

    def _build_communication_graph(self):
        """Build communication topology between robots"""
        # For now, use full connectivity (can be modified for different topologies)
        graph = {}
        for i in range(self.num_robots):
            graph[i] = [j for j in range(self.num_robots) if j != i]
        return graph

    def forward_with_communication(self, robot_id, g, feat_dict, communication_rounds=1):
        """
        Forward pass for a specific robot with communication.

        Args:
            robot_id: ID of the robot making the decision
            g: Graph for this robot
            feat_dict: Features for this robot
            communication_rounds: Number of communication rounds
        """
        robot_net = self.robot_networks[robot_id]

        if communication_rounds == 0:
            # No communication, just local decision
            return robot_net(g, feat_dict, use_communication=False)

        # Multi-round communication
        for round_num in range(communication_rounds):
            # Collect messages from neighbors
            neighbor_messages = {}
            neighbors = self.communication_graph.get(robot_id, [])

            for neighbor_id in neighbors:
                if neighbor_id < len(self.robot_networks):
                    # Get communication message from neighbor
                    # Note: In practice, this would use the neighbor's current state
                    # For now, we'll use a simplified approach
                    try:
                        neighbor_net = self.robot_networks[neighbor_id]
                        message = neighbor_net.get_communication_message(g, feat_dict)
                        neighbor_messages[neighbor_id] = message
                    except:
                        neighbor_messages[neighbor_id] = None

            # Set neighbor messages and forward
            robot_net.set_neighbor_messages(neighbor_messages)

        return robot_net(g, feat_dict, use_communication=True)

    def get_robot_network(self, robot_id):
        """Get the network for a specific robot"""
        if 0 <= robot_id < len(self.robot_networks):
            return self.robot_networks[robot_id]
        return None

# device = torch.device('cuda')
# use this for CPU-only mode
device = torch.device('cpu')

in_dim = {'task': 6,
          'loc': 1,
          'robot': 1,
          'state': 4,
          'value': 1
          }

hid_dim = {'task': 64,
           'loc': 64,
           'robot': 64,
           'state': 64,
           'value': 64
           }

out_dim = {'task': 32,
          'loc': 32,
          'robot': 32,
          'state': 32,
          'value': 1
          }

cetypes = [('task', 'temporal', 'task'),
           ('task', 'located_in', 'loc'),('loc', 'near', 'loc'),
           ('task', 'assigned_to', 'robot'), ('robot', 'com', 'robot'),
           ('task', 'tin', 'state'), ('loc', 'lin', 'state'), 
           ('robot', 'rin', 'state'), ('state', 'sin', 'state'), 
           ('task', 'tto', 'value'), ('robot', 'rto', 'value'), 
           ('state', 'sto', 'value'), ('value', 'vto', 'value'),
           ('task', 'take_time', 'robot'), ('robot', 'use_time', 'task')]

num_heads = 8

policy_net = ScheduleNet4Layer(in_dim, hid_dim, out_dim, cetypes, num_heads).to(device)
policy_net.eval()
 

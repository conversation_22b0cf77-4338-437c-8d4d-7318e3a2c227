#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
train_superior_decentralized.py

Enhanced decentralized multi-objective training for SUPERIOR performance vs baselines.
Designed to achieve:
- Training loss < 1 and gradually decreasing
- Makespan significantly better than EDF/Tercio baselines
- Workload balance significantly better than baselines
"""

import os
import sys
import time
import argparse
import numpy as np
import torch
import torch.nn.functional as F
from torch.nn.utils import clip_grad_norm_
from torch.optim.lr_scheduler import OneCycleLR
from torch.optim.swa_utils import AveragedModel
from torch.utils.tensorboard import SummaryWriter
import copy

# Import required modules
from utils import build_hetgraph, hetgraph_node_helper
from hetnet import MultiRobotDecentralizedSystem
from decentralized_multi_objective_train import (
    collect_decentralized_data, 
    _calculate_workload_balance_loss,
    _estimate_makespan_from_transition,
    _calculate_experience_priority
)


def train_superior_decentralized_system(memories, num_robots, args):
    """
    Train the decentralized system with SUPERIOR performance optimizations.
    """
    device = torch.device(args.device)
    
    # Enhanced network architecture for better performance
    in_dim = {'task': 6, 'loc': 1, 'robot': 1, 'state': 4, 'value': 1}
    hid_dim = {'task': 128, 'loc': 128, 'robot': 128, 'state': 128, 'value': 128}  # Larger hidden dims
    out_dim = {'task': 64, 'loc': 64, 'robot': 64, 'state': 64, 'value': 1}  # Larger output dims
    
    cetypes = [
        ('task', 'temporal', 'task'), ('task', 'located_in', 'loc'), 
        ('loc', 'near', 'loc'), ('task', 'assigned_to', 'robot'), 
        ('robot', 'com', 'robot'), ('task', 'tin', 'state'), 
        ('loc', 'lin', 'state'), ('robot', 'rin', 'state'), 
        ('state', 'sin', 'state'), ('task', 'tto', 'value'), 
        ('robot', 'rto', 'value'), ('state', 'sto', 'value'), 
        ('value', 'vto', 'value'), ('task', 'take_time', 'robot'), 
        ('robot', 'use_time', 'task')
    ]
    
    # Create enhanced system
    system = MultiRobotDecentralizedSystem(in_dim, hid_dim, out_dim, cetypes, num_robots, 10).to(device)
    
    # Target network for stable learning
    target_system = copy.deepcopy(system)
    target_update_period = 15  # Update target every 15 steps
    
    # EMA for smoother performance
    ema = AveragedModel(system, avg_fn=lambda avg, new, num_averaged: 0.99*avg + 0.01*new)
    
    # SUPERIOR optimizers with higher learning rate
    optimizers = [torch.optim.AdamW(
        system.get_robot_network(i).parameters(), 
        lr=5e-4,  # Higher learning rate for better convergence
        weight_decay=5e-5,  # Lower weight decay
        eps=1e-8, 
        betas=(0.9, 0.999)
    ) for i in range(num_robots)]
    
    # Enhanced scheduler for superior performance
    schedulers = [
        OneCycleLR(
            opt,
            max_lr=5e-4,  # Match optimizer learning rate
            total_steps=args.steps,
            pct_start=0.3,  # Longer warmup for stability
            div_factor=5,   # Less aggressive initial reduction
            final_div_factor=10  # Moderate final reduction
        )
        for opt in optimizers
    ]
    
    # SUPERIOR loss scaling for beating baselines
    conf_scale = 0.8    # Higher for better confidence learning
    balance_scale = 5.0  # Much higher to beat baseline balance performance
    makespan_scale = 4.0  # Higher for better makespan optimization
    
    # Training setup
    writer = SummaryWriter(log_dir=args.tbdir)
    
    print(f"🚀 Starting SUPERIOR decentralized training for {args.steps} steps")
    print(f"📊 Enhanced loss scaling: conf={conf_scale}, balance={balance_scale}, makespan={makespan_scale}")
    print(f"🧠 Higher learning rate: {5e-4}, superior architecture, stronger performance focus")
    
    # Training loop
    for step in range(1, args.steps + 1):
        print(f"\n🔄 Starting training step {step}/{args.steps}")
        start_time = time.time()
        total_loss = 0.0
        step_rewards = []
        step_makespans = []
        step_balances = []
        
        # Enhanced loss tracking
        loss_components = {
            'conf_loss': 0.0, 'balance_loss': 0.0, 'makespan_penalty': 0.0,
            'exploration': 0.0, 'reward_shaping': 0.0
        }
        
        # Train each robot
        for robot_id in range(num_robots):
            # Enhanced prioritized sampling
            batch = []
            memory = memories[robot_id]
            
            # Use smaller batch size if memory is small
            min_batch_size = 4
            if len(memory) < min_batch_size:
                print(f"⚠️  Robot {robot_id} has only {len(memory)} experiences, skipping...")
                continue
                
            # Better prioritized sampling with higher probability for good experiences
            priorities = [_calculate_experience_priority(exp) for exp in memory]
            priorities = np.array(priorities)
            
            # Enhanced sampling with temperature
            temperature = max(0.3, 1.0 - step / args.steps)  # Decay temperature
            probs = F.softmax(torch.tensor(priorities) / temperature, dim=0).numpy()
            
            # Sample with replacement for better diversity
            batch_size = min(12, len(memory))  # Larger batch size for better learning
            indices = np.random.choice(len(memory), size=batch_size, p=probs, replace=True)
            batch = [memory[i] for i in indices]
            
            print(f"🤖 Robot {robot_id}: Processing {len(batch)} experiences...")
            
            # Forward pass with enhanced communication
            loss = 0.0
            for t in batch:
                unsch = [tid for tid in range(1, t.durs.shape[0]+1) if tid not in t.curr_partialw]
                if not unsch:
                    continue
                    
                # Build graph and features
                g = build_hetgraph(
                    t.curr_g, t.durs.shape[0], num_robots, t.durs.astype(np.float32), 
                    6, np.array(t.locs, dtype=np.int64), 1, t.curr_partials, 
                    np.array(unsch, dtype=np.int32), robot_id, np.array(unsch, dtype=np.int32)
                ).to(device)
                
                feat = hetgraph_node_helper(
                    t.curr_g.number_of_nodes(), t.curr_partialw, t.curr_partials, 
                    t.locs, t.durs, 6, num_robots, len(unsch)
                )
                feat = {k: torch.tensor(v, device=device, dtype=torch.float32) for k, v in feat.items()}
                
                # Enhanced forward pass with more communication rounds
                out = system.forward_with_communication(robot_id, g, feat, communication_rounds=10)
                q, conf = out['value'], out['confidence']
                
                # Enhanced target calculation using target network
                with torch.no_grad():
                    target_out = target_system.forward_with_communication(robot_id, g, feat, communication_rounds=10)
                    target_q = target_out['value']
                    baseline_value = torch.mean(target_q).item() if len(target_q) > 0 else 0.0
                    
                # SUPERIOR reward shaping for better performance vs baselines
                raw_reward = float(t.reward_n)
                if raw_reward > 0:
                    # Amplify good rewards significantly
                    enhanced_reward = min(10.0, raw_reward * 2.0)
                elif raw_reward < -5:
                    # Moderate bad rewards to avoid overwhelming gradients
                    enhanced_reward = max(-10.0, raw_reward * 0.5)
                else:
                    # Normal scaling for moderate rewards
                    enhanced_reward = np.clip(raw_reward, -10.0, 10.0)
                
                targets = torch.full((len(unsch), 1), baseline_value, device=device)
                weights_task = torch.full((len(unsch), 1), 1.0/(len(unsch)), device=device)
                idx = unsch.index(t.act_task) if t.act_task in unsch else 0
                targets[idx] = enhanced_reward
                weights_task[idx] = 3.0  # Higher weight for selected action
                
                # Enhanced loss calculation
                # Huber loss for robustness
                huber_loss = F.smooth_l1_loss(q, targets, reduction='none')
                conf_loss = (huber_loss * weights_task).mean()
                
                # Enhanced confidence regularization
                conf_reg = torch.mean((conf - 0.8)**2)  # Target higher confidence
                
                # Enhanced workload balance loss
                probs = F.softmax(q.squeeze(), dim=0)
                balance_loss, current_balance_std = _calculate_workload_balance_loss(
                    t, robot_id, probs, num_robots, device, balance_target=0.2  # Stricter balance target
                )
                
                # Enhanced makespan penalty
                estimated_makespan = _estimate_makespan_from_transition(t, device)
                makespan_penalty = torch.clamp(estimated_makespan - 6.0, min=0.0, max=25.0)  # Target makespan < 6
                
                # Enhanced exploration with better scheduling
                entropy = -torch.sum(probs * torch.log(probs + 1e-8))
                exploration_coef = max(0.05, 0.2 * (1 - step / args.steps))
                exploration_bonus = exploration_coef * entropy
                
                # Reward shaping bonus
                reward_shaping = 0.0
                if raw_reward > 0:
                    reward_shaping = min(2.0, raw_reward / 15.0)
                elif raw_reward < -8:
                    reward_shaping = -1.0
                
                # SUPERIOR total loss
                total_loss_component = (
                    conf_loss * conf_scale +
                    conf_reg * conf_scale * 0.3 +
                    balance_loss * balance_scale +
                    makespan_penalty * makespan_scale -
                    exploration_bonus +
                    reward_shaping
                )
                
                loss += total_loss_component
                
                # Track components
                loss_components['conf_loss'] += (conf_loss * conf_scale).item()
                loss_components['balance_loss'] += (balance_loss * balance_scale).item()
                loss_components['makespan_penalty'] += (makespan_penalty * makespan_scale).item()
                loss_components['exploration'] += exploration_bonus.item()
                loss_components['reward_shaping'] += reward_shaping if hasattr(reward_shaping, 'item') else reward_shaping
                
                step_rewards.append(float(t.reward_n))
                if estimated_makespan.item() > 0:
                    step_makespans.append(estimated_makespan.item())
                step_balances.append(current_balance_std)
            
            if loss.item() > 0:
                print(f"  📈 Robot {robot_id}: loss={loss.item():.4f}")
                # Enhanced optimization
                optimizers[robot_id].zero_grad()
                loss.backward()
                
                # Gradient clipping for stability
                clip_grad_norm_(system.get_robot_network(robot_id).parameters(), max_norm=2.0)
                
                optimizers[robot_id].step()
                schedulers[robot_id].step()
                
                # Update EMA
                ema.update_parameters(system)
                
                total_loss += loss.item()
            else:
                print(f"  ⚠️  Robot {robot_id}: zero loss, skipping optimization")
        
        # Update target network periodically
        if step % target_update_period == 0:
            target_system.load_state_dict(system.state_dict())
        
        # Enhanced logging
        runtime = time.time() - start_time
        print(f"✅ Step {step} completed in {runtime:.2f}s, total_loss={total_loss:.4f}")
        
        if step % 5 == 0:  # More frequent logging
            avg_reward = np.mean(step_rewards) if step_rewards else 0.0
            avg_makespan = np.mean(step_makespans) if step_makespans else 0.0
            avg_balance = np.mean(step_balances) if step_balances else 0.0
            
            print(f"📊 Step {step:4d}: loss={total_loss:.3f}, reward={avg_reward:.2f}, "
                  f"makespan={avg_makespan:.1f}, balance={avg_balance:.2f}")
            
            # TensorBoard logging
            writer.add_scalar('Loss/Total', total_loss, step)
            writer.add_scalar('Performance/Reward', avg_reward, step)
            writer.add_scalar('Performance/Makespan', avg_makespan, step)
            writer.add_scalar('Performance/Balance', avg_balance, step)
            
            for component, value in loss_components.items():
                writer.add_scalar(f'Loss/{component}', value, step)
        
        # Save checkpoints
        if step % args.checkpoint_interval == 0:
            checkpoint = {
                'step': step,
                'robot_networks': {f'robot_{i}': system.get_robot_network(i).state_dict() 
                                 for i in range(num_robots)},
                'target_state': target_system.state_dict(),
                'ema_state': ema.state_dict(),
                'optimizers': [opt.state_dict() for opt in optimizers],
                'schedulers': [sch.state_dict() for sch in schedulers],
                'num_robots': num_robots,
                'alpha': args.alpha,
                'beta': args.beta,
                'loss_components': loss_components
            }
            
            checkpoint_path = os.path.join(args.cpsave, f"superior_decentralized_step_{step}.pth")
            os.makedirs(args.cpsave, exist_ok=True)
            torch.save(checkpoint, checkpoint_path)
            print(f"💾 Saved superior checkpoint: {checkpoint_path}")
    
    writer.close()
    print("🎉 SUPERIOR decentralized training completed!")


def main():
    parser = argparse.ArgumentParser(description="Superior Decentralized Multi-Objective Training")
    parser.add_argument("--path-to-train", default="./problem_instances/constraints")
    parser.add_argument("--train-start-no", type=int, default=1)
    parser.add_argument("--train-end-no", type=int, default=100)
    parser.add_argument("--num-robots", type=int, default=2)
    parser.add_argument("--alpha", type=float, default=0.2)  # Lower alpha for more balance focus
    parser.add_argument("--beta", type=float, default=0.8)   # Higher beta for balance
    parser.add_argument("--steps", type=int, default=1000)
    parser.add_argument("--checkpoint-interval", type=int, default=50)
    parser.add_argument("--cpsave", default="./cp_superior_decentralized")
    parser.add_argument("--tbdir", default="./runs/SuperiorDecentralized")
    parser.add_argument("--device", default="cpu")
    
    args = parser.parse_args()
    
    print(f"🚀 SUPERIOR Decentralized Training: α={args.alpha}, β={args.beta}")
    print(f"📊 Focus: Maximum balance weight (β={args.beta}) for SUPERIOR workload distribution")
    
    # Collect training data
    memories = collect_decentralized_data(
        args.path_to_train, args.train_start_no, args.train_end_no, 
        args.num_robots, args.alpha, args.beta
    )
    
    if sum(len(m) for m in memories) == 0:
        print("❌ No training data found. Exiting.")
        return
    
    print(f"📚 Collected {sum(len(m) for m in memories)} training experiences")
    
    # Train superior system
    train_superior_decentralized_system(memories, args.num_robots, args)


if __name__ == "__main__":
    main()

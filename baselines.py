#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
makespan_for_each.py

Compute and save makespans (and runtimes/feasibility) for various schedulers:
  – edf      (Earliest‐Deadline‐First)
  – tercio   (Simple round‐robin/grouping heuristic)
  – random   (Random assignment order)
  – gurobi   (Exact MILP via Gurobi)
  – ssan     (Learned SSAN policy via HeteroGAT)

Usage example:
  python3 makespan_for_each.py \
    --solver edf \
    --path-test-data ./problem_instances_test/constraints \
    --num-tasks 5 \
    --num-robots 2 \
    --max-instances 1000 \
    --output-folder ./csv_outputs \
    --device cpu
"""

import os
import argparse
import time
import csv
import numpy as np
import torch
import networkx as nx

# Try to import Gurobi - make it optional
try:
    import gurobipy as gp
    from gurobipy import GRB
    GUROBI_AVAILABLE = True
except ImportError:
    print("Warning: Gurobi not available. Gurobi-based solvers will be disabled.")
    GUROBI_AVAILABLE = False

from utils import SchedulingEnv
from benchmark.JohnsonUltra import johnson<PERSON>
from hetnet import ScheduleNet4Layer
from utils import action_helper_rollout, hetgraph_node_helper, build_hetgraph

# Try to import workload balance solvers - make them optional
try:
    from workload_balance_solvers import (solve_workload_balance_gurobi,
                                         solve_multi_objective_gurobi,
                                         solve_workload_balance_heuristic)
    WORKLOAD_SOLVERS_AVAILABLE = True
except ImportError:
    print("Warning: Workload balance solvers not available.")
    WORKLOAD_SOLVERS_AVAILABLE = False

from multi_objective_utils import calculate_workload_balance

def get_ready_tasks(env):
    """
    Get tasks that are ready to be scheduled (have all prerequisites satisfied).
    Returns a list of task IDs that can be scheduled now.
    """
    ready_tasks = []

    # Get all unscheduled tasks
    for i in range(1, env.num_tasks + 1):
        if i not in env.partialw:
            # Check if all predecessors are scheduled
            task_node = f"f{i:03d}"  # Format as f001, f002, etc.

            # Check if the node exists in the graph
            if task_node not in env.halfDG:
                # If node doesn't exist, skip this task
                continue

            try:
                preds = list(env.halfDG.predecessors(task_node))
                all_preds_scheduled = True

                for pred in preds:
                    # Extract task number from predecessor node name
                    if pred.startswith('f') and pred != 'f000':
                        pred_task_id = int(pred[1:])  # Extract number from f001 -> 1
                        if pred_task_id not in env.partialw:
                            all_preds_scheduled = False
                            break

                if all_preds_scheduled:
                    ready_tasks.append(i)

            except nx.NetworkXError:
                # If there's an error accessing predecessors, skip this task
                continue

    return ready_tasks

def detect_negative_cycle(G):
    """
    Returns True if JohnsonU reports a negative cycle on G.
    """
    try:
        _, _ = johnsonU(G)
        return False
    except Exception:
        return True

def load_env_from_prefix(prefix):
    """
    Given a prefix like "./constraints/00001", loads dur/ddl/wait/loc,
    constructs SchedulingEnv, initializes its STN, and sets min_makespan.
    """
    try:
        env = SchedulingEnv(prefix)

        # Ensure the graph is properly initialized
        if not hasattr(env, 'halfDG') or env.halfDG is None:
            print(f"Warning: halfDG not initialized for {prefix}, calling check_consistency_makespan")
            ok, mm = env.check_consistency_makespan(updateDG=True)
        else:
            ok, mm = env.check_consistency_makespan(updateDG=False)

        if not ok:
            raise RuntimeError(f"Initial STN infeasible at {prefix}")
        env.min_makespan = mm

        # Verify that expected nodes exist in the graph
        expected_nodes = [f"f{i:03d}" for i in range(1, env.num_tasks + 1)]
        missing_nodes = [node for node in expected_nodes if node not in env.halfDG]
        if missing_nodes:
            print(f"Warning: Missing nodes in graph for {prefix}: {missing_nodes}")

        return env

    except Exception as e:
        print(f"Error loading environment from {prefix}: {e}")
        raise RuntimeError(f"Failed to load environment from {prefix}: {e}")

def solve_with_edf(prefix, num_tasks, num_robots):
    """
    EDF: at each step, pick the unscheduled task with the smallest deadline;
    assign it to the robot that yields the smallest immediate STN makespan.
    Returns: (final_makespan, assignment_list, runtime, feasible_flag)
    """
    t0 = time.time()
    try:
        env = load_env_from_prefix(prefix)
    except RuntimeError:
        return float("nan"), [], time.time() - t0, False

    assignment_order = []
    feasible_flag = True

    # Repeat until all tasks scheduled (or infeasible):
    while True:
        # Gather unscheduled tasks and their deadlines
        candidates = []
        for i in range(1, num_tasks + 1):
            if i not in env.partialw:
                ddl_i = np.inf
                for (tid, dl) in env.ddl:
                    if tid == i:
                        ddl_i = dl
                        break
                candidates.append((i, ddl_i))
        if not candidates:
            break

        # Choose task with minimum deadline
        candidates.sort(key=lambda x: x[1])
        chosen_task = int(candidates[0][0])

        # Evaluate assigning chosen_task to each robot
        best_r, best_mk = None, float("inf")
        for r in range(num_robots):
            # Make a fresh copy of env
            tmp_env = SchedulingEnv.__new__(SchedulingEnv)
            tmp_env.dur         = env.dur.copy()
            tmp_env.ddl         = env.ddl.copy()
            tmp_env.wait        = env.wait.copy()
            tmp_env.loc         = env.loc.copy()
            tmp_env.num_tasks   = env.num_tasks
            tmp_env.num_robots  = env.num_robots
            tmp_env.M           = env.M
            tmp_env.C           = env.C
            tmp_env.max_deadline= env.max_deadline
            tmp_env.partials    = [p.copy() for p in env.partials]
            tmp_env.partialw    = env.partialw.copy()
            tmp_env.g           = env.g.copy()

            # Set min_makespan on the copy before any insertion
            ok2, tmp_mm = tmp_env.check_consistency_makespan(updateDG=False)
            if not ok2:
                continue
            tmp_env.min_makespan = tmp_mm

            ok3, _, _ = tmp_env.insert_robot(chosen_task, r)
            if not ok3:
                continue

            ok4, mk_candidate = tmp_env.check_consistency_makespan(updateDG=False)
            if ok4 and mk_candidate < best_mk:
                best_r, best_mk = r, mk_candidate

        if best_r is None:
            feasible_flag = False
            break

        ok5, _, done_flag = env.insert_robot(chosen_task, best_r)
        if not ok5:
            feasible_flag = False
            break

        assignment_order.append((chosen_task, best_r))
        if done_flag:
            break

    runtime = time.time() - t0
    if not feasible_flag:
        return float("nan"), assignment_order, runtime, False

    ok6, final_mk = env.check_consistency_makespan(updateDG=False)
    if not ok6:
        return float("nan"), assignment_order, runtime, False

    return final_mk, assignment_order, runtime, True

def solve_with_tercio(prefix, num_tasks, num_robots):
    """
    TERcio: simple grouping heuristic:
      – Round‐robin: assign tasks 1,2,...,N in numeric order to robots 0,1,...,R-1 cyclically.
      – Then check STN feasibility and compute makespan.
    Returns: (final_makespan, assignment_list, runtime, feasible_flag)
    """
    t0 = time.time()
    try:
        env = load_env_from_prefix(prefix)
    except RuntimeError:
        return float("nan"), [], time.time() - t0, False

    # Create a round‐robin assignment order
    assignment_order = []
    for idx, task in enumerate(range(1, num_tasks + 1)):
        r = idx % num_robots
        assignment_order.append((task, r))

    feasible_flag = True
    for (task, r) in assignment_order:
        # Make a fresh copy to test feasibility before actually inserting
        tmp_env = SchedulingEnv.__new__(SchedulingEnv)
        tmp_env.dur         = env.dur.copy()
        tmp_env.ddl         = env.ddl.copy()
        tmp_env.wait        = env.wait.copy()
        tmp_env.loc         = env.loc.copy()
        tmp_env.num_tasks   = env.num_tasks
        tmp_env.num_robots  = env.num_robots
        tmp_env.M           = env.M
        tmp_env.C           = env.C
        tmp_env.max_deadline= env.max_deadline
        tmp_env.partials    = [p.copy() for p in env.partials]
        tmp_env.partialw    = env.partialw.copy()
        tmp_env.g           = env.g.copy()

        ok2, tmp_mm = tmp_env.check_consistency_makespan(updateDG=False)
        if not ok2:
            feasible_flag = False
            break
        tmp_env.min_makespan = tmp_mm

        ok3, _, _ = tmp_env.insert_robot(task, r)
        if not ok3:
            feasible_flag = False
            break

        ok4, _ = tmp_env.check_consistency_makespan(updateDG=False)
        if not ok4:
            feasible_flag = False
            break

        # Finally insert into real env
        ok5, _, done_flag = env.insert_robot(task, r)
        if not ok5:
            feasible_flag = False
            break
        if done_flag:
            break

    runtime = time.time() - t0
    if not feasible_flag:
        return float("nan"), [], runtime, False

    ok6, final_mk = env.check_consistency_makespan(updateDG=False)
    if not ok6:
        return float("nan"), assignment_order, runtime, False

    return final_mk, assignment_order, runtime, True

def solve_with_random(prefix, num_tasks, num_robots):
    """
    RANDOM: shuffle the task list uniformly at random, assign in that order
    to the robot resulting in minimal STN makespan at each step.
    Returns: (final_makespan, assignment_list, runtime, feasible_flag)
    """
    t0 = time.time()
    try:
        env = load_env_from_prefix(prefix)
    except RuntimeError:
        return float("nan"), [], time.time() - t0, False

    tasks = list(range(1, num_tasks + 1))
    np.random.shuffle(tasks)

    assignment_order = []
    feasible_flag = True

    for chosen_task in tasks:
        best_r, best_mk = None, float("inf")
        for r in range(num_robots):
            tmp_env = SchedulingEnv.__new__(SchedulingEnv)
            tmp_env.dur         = env.dur.copy()
            tmp_env.ddl         = env.ddl.copy()
            tmp_env.wait        = env.wait.copy()
            tmp_env.loc         = env.loc.copy()
            tmp_env.num_tasks   = env.num_tasks
            tmp_env.num_robots  = env.num_robots
            tmp_env.M           = env.M
            tmp_env.C           = env.C
            tmp_env.max_deadline= env.max_deadline
            tmp_env.partials    = [p.copy() for p in env.partials]
            tmp_env.partialw    = env.partialw.copy()
            tmp_env.g           = env.g.copy()

            ok2, tmp_mm = tmp_env.check_consistency_makespan(updateDG=False)
            if not ok2:
                continue
            tmp_env.min_makespan = tmp_mm

            ok3, _, _ = tmp_env.insert_robot(chosen_task, r)
            if not ok3:
                continue

            ok4, mk_candidate = tmp_env.check_consistency_makespan(updateDG=False)
            if ok4 and mk_candidate < best_mk:
                best_r, best_mk = r, mk_candidate

        if best_r is None:
            feasible_flag = False
            break

        ok5, _, done_flag = env.insert_robot(chosen_task, best_r)
        if not ok5:
            feasible_flag = False
            break

        assignment_order.append((chosen_task, best_r))
        if done_flag:
            break

    runtime = time.time() - t0
    if not feasible_flag:
        return float("nan"), [], runtime, False

    ok6, final_mk = env.check_consistency_makespan(updateDG=False)
    if not ok6:
        return float("nan"), assignment_order, runtime, False

    return final_mk, assignment_order, runtime, True

def solve_with_gurobi(prefix, num_tasks, num_robots):
    """
    Exact MILP via Gurobi. Each task assigned to exactly one robot,
    minimize makespan. Returns (makespan, assignments, runtime, feasible_flag).
    """
    t0 = time.time()
    try:
        env = load_env_from_prefix(prefix)
    except RuntimeError:
        return float("nan"), {}, time.time() - t0, False

    tasks = list(range(1, num_tasks + 1))
    robots = list(range(num_robots))

    model = gp.Model("makespan")
    model.setParam("OutputFlag", 0)

    start_times = model.addVars(tasks, lb=0, vtype=GRB.CONTINUOUS, name="start")
    x = model.addVars(tasks, robots, vtype=GRB.BINARY, name="assign")
    makespan = model.addVar(lb=0, vtype=GRB.CONTINUOUS, name="makespan")
    model.setObjective(makespan, GRB.MINIMIZE)

    # Each task assigned exactly once
    for t in tasks:
        model.addConstr(gp.quicksum(x[t, r] for r in robots) == 1, f"assign_{t}")

    # Duration ≤ makespan
    for t in tasks:
        model.addConstr(
            start_times[t] + gp.quicksum(x[t, r] * env.dur[t - 1, r] for r in robots)
            <= makespan,
            f"dur_{t}"
        )

    # Deadlines
    for (tid, dl) in env.ddl:
        if tid in tasks:
            model.addConstr(start_times[tid] <= dl, f"deadline_{tid}")

    # Waiting constraints
    for (ti, tj, wt) in env.wait:
        model.addConstr(start_times[ti] + wt <= start_times[tj], f"wait_{ti}_{tj}")

    model.optimize()
    if model.status != GRB.OPTIMAL:
        return float("nan"), {}, time.time() - t0, False

    assignments = {r: [] for r in robots}
    for t in tasks:
        for r in robots:
            if x[t, r].X > 0.5:
                assignments[r].append(t)
                break

    # Sort by start time
    for r in robots:
        assignments[r].sort(key=lambda task: start_times[task].X)

    final_mk = model.objVal
    runtime = time.time() - t0
    return final_mk, assignments, runtime, True

def solve_with_ssan(prefix, num_tasks, num_robots, checkpoint_path, map_width=6, loc_dist_threshold=1, device="cpu"):
    """
    Greedy SSAN rollout: at each step, build a hetgraph for each robot choice,
    compute Q-values for all unscheduled tasks, pick (r,t) with highest Q.
    Returns (makespan, assignment_list, runtime, feasible_flag).
    """
    t0 = time.time()
    # Load env
    try:
        env = load_env_from_prefix(prefix)
    except RuntimeError:
        return float("nan"), [], time.time() - t0, False

    # Load SSAN policy net
    in_dim = {'task': 6, 'loc': 1, 'robot': 1, 'state': 4, 'value': 1}
    hid_dim= {'task': 64, 'loc': 64, 'robot': 64, 'state': 64, 'value': 64}
    out_dim= {'task': 32, 'loc': 32, 'robot': 32, 'state': 32, 'value': 1}
    cetypes = [
        ('task','temporal','task'),
        ('task','located_in','loc'), ('loc','near','loc'),
        ('task','assigned_to','robot'), ('robot','com','robot'),
        ('task','tin','state'), ('loc','lin','state'),
        ('robot','rin','state'), ('state','sin','state'),
        ('task','tto','value'), ('robot','rto','value'),
        ('state','sto','value'), ('value','vto','value'),
        ('task','take_time','robot'), ('robot','use_time','task')
    ]
    num_heads = 8

    policy_net = ScheduleNet4Layer(in_dim, hid_dim, out_dim, cetypes, num_heads).to(device)
    ckpt = torch.load(checkpoint_path, map_location=device)
    policy_net.load_state_dict(ckpt['policy_net_state_dict'])
    policy_net.eval()

    assignment_order = []
    feasible_flag = True

    while True:
        unsch_tasks = env.get_unscheduled_tasks()
        if len(unsch_tasks) == 0:
            break

        best_r, best_t, best_q = None, None, -float("inf")
        for r in range(num_robots):
            # Build one hetgraph with selected_robot=r
            g = build_hetgraph(
                env.halfDG,
                num_tasks,
                num_robots,
                env.dur,
                map_width,
                np.array(env.loc, dtype=np.int64),
                loc_dist_threshold,
                env.partials,
                unsch_tasks,
                r,
                unsch_tasks
            ).to(device)

            feat_dict = hetgraph_node_helper(
                env.halfDG.number_of_nodes(),
                env.partialw,
                env.partials,
                env.loc,
                env.dur,
                map_width,
                num_robots,
                len(unsch_tasks)
            )
            feat_tensors = {k: torch.tensor(v, device=device).float() for k, v in feat_dict.items()}
            outputs = policy_net(g, feat_tensors)
            qvals = outputs['value'].detach().cpu().numpy().reshape(-1)  # size = len(unsch_tasks)

            # Find best among (r, all unsch_tasks)
            idx = np.argmax(qvals)
            if qvals[idx] > best_q:
                best_q = float(qvals[idx])
                best_r = r
                best_t = int(unsch_tasks[idx])

        # Try inserting (best_t, best_r)
        ok, _, done_flag = env.insert_robot(best_t, best_r)
        if not ok:
            feasible_flag = False
            break

        assignment_order.append((best_t, best_r))
        if done_flag:
            break

    runtime = time.time() - t0
    if not feasible_flag:
        return float("nan"), assignment_order, runtime, False

    ok_final, final_mk = env.check_consistency_makespan(updateDG=False)
    if not ok_final:
        return float("nan"), assignment_order, runtime, False

    return final_mk, assignment_order, runtime, True

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--solver",
        choices=["edf", "tercio", "random", "gurobi", "ssan",
                "workload_balance", "multi_objective", "balance_heuristic"],
        required=True,
        help="Which solver to run"
    )
    parser.add_argument(
        "--path-test-data",
        required=True,
        help="Path to test constraints folder (contains *_dur.txt, *_ddl.txt, etc.)"
    )
    parser.add_argument(
        "--num-tasks", type=int, required=True, help="Number of tasks per instance"
    )
    parser.add_argument(
        "--num-robots", type=int, required=True, help="Number of robots per instance"
    )
    parser.add_argument(
        "--max-instances", type=int, required=True, help="How many instances to evaluate"
    )
    parser.add_argument(
        "--output-folder", required=True, help="Folder to write CSV(s) into"
    )
    parser.add_argument(
        "--checkpoint-policy", default=None,
        help="Path to SSAN checkpoint (required if solver=ssan)"
    )
    parser.add_argument(
        "--device", default="cpu", help="Torch device (cpu or cuda)"
    )
    parser.add_argument(
        "--alpha", type=float, default=0.5,
        help="Weight for makespan objective in multi-objective optimization (0-1)"
    )
    parser.add_argument(
        "--beta", type=float, default=0.5,
        help="Weight for workload balance objective in multi-objective optimization (0-1)"
    )
    args = parser.parse_args()

    os.makedirs(args.output_folder, exist_ok=True)
    solver = args.solver
    num_tasks = args.num_tasks
    num_robots = args.num_robots
    max_inst = args.max_instances
    dev = torch.device(args.device)

    # Prepare CSV output
    csv_path = os.path.join(args.output_folder, f"makespans_{solver}.csv")
    with open(csv_path, mode="w", newline="") as csvfile:
        writer = csv.writer(csvfile)
        header = ["instance_id", "makespan", "feasible", "runtime"]
        writer.writerow(header)

        for inst_id in range(1, max_inst + 1):
            prefix = os.path.join(args.path_test_data, f"{inst_id:05d}")
            # Check that the dur file exists
            if not os.path.isfile(f"{prefix}_dur.txt"):
                print(f"[Instance {inst_id:05d}] No *_dur.txt found, skipping.")
                continue

            if solver == "edf":
                mk, assigns, rt, ok = solve_with_edf(prefix, num_tasks, num_robots)
            elif solver == "tercio":
                mk, assigns, rt, ok = solve_with_tercio(prefix, num_tasks, num_robots)
            elif solver == "random":
                mk, assigns, rt, ok = solve_with_random(prefix, num_tasks, num_robots)
            elif solver == "gurobi":
                mk, assigns, rt, ok = solve_with_gurobi(prefix, num_tasks, num_robots)
            elif solver == "ssan":
                if args.checkpoint_policy is None:
                    raise RuntimeError("Must provide --checkpoint-policy when solver=ssan")
                mk, assigns, rt, ok = solve_with_ssan(
                    prefix,
                    num_tasks,
                    num_robots,
                    args.checkpoint_policy,
                    map_width=6,
                    loc_dist_threshold=1,
                    device=dev
                )
            elif solver == "workload_balance":
                if not WORKLOAD_SOLVERS_AVAILABLE or not GUROBI_AVAILABLE:
                    print(f"Skipping {prefix}: Workload balance solvers or Gurobi not available")
                    continue
                # Pure workload balance optimization
                balance_score, assigns, rt, ok = solve_workload_balance_gurobi(prefix, num_tasks, num_robots)
                mk = balance_score  # Store balance score as "makespan" for CSV compatibility
            elif solver == "multi_objective":
                if not WORKLOAD_SOLVERS_AVAILABLE or not GUROBI_AVAILABLE:
                    print(f"Skipping {prefix}: Workload balance solvers or Gurobi not available")
                    continue
                # Multi-objective optimization
                mk, balance_score, assigns, rt, ok = solve_multi_objective_gurobi(
                    prefix, num_tasks, num_robots, args.alpha, args.beta
                )
            elif solver == "balance_heuristic":
                if not WORKLOAD_SOLVERS_AVAILABLE:
                    print(f"Skipping {prefix}: Workload balance solvers not available")
                    continue
                # Heuristic workload balance optimization
                balance_score, assigns, rt, ok = solve_workload_balance_heuristic(prefix, num_tasks, num_robots)
                mk = balance_score  # Store balance score as "makespan" for CSV compatibility
            else:
                raise RuntimeError(f"Unknown solver: {solver}")

            writer.writerow([f"{inst_id:05d}", mk, int(ok), rt])
            print(f"[Instance {inst_id:05d}] solver={solver}, makespan={mk}, feasible={ok}, time={rt:.2f}s")

    print(f"Finished. Results saved to {csv_path}")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
train_improved_decentralized.py

Improved training script for decentralized multi-objective scheduling model.
Incorporates all performance improvements to beat baseline solvers.
"""

import os
import argparse
import time
import copy
import numpy as np
import torch
import copy

import torch.nn.functional as F
from torch.optim.lr_scheduler import OneCycleLR
from torch.nn.utils import clip_grad_norm_
from torch.optim.swa_utils import AveragedModel
from torch.utils.tensorboard import SummaryWriter

from utils import SchedulingEnv, build_hetgraph, hetgraph_node_helper
from hetnet import MultiRobotDecentralizedSystem
from multi_objective_utils import calculate_workload_balance
from decentralized_multi_objective_train import (
    collect_decentralized_data, 
    _calculate_workload_balance_loss,
    _estimate_makespan_from_transition,
    _calculate_experience_priority,
    MultiObjectiveMetrics
)


def train_improved_decentralized_system(memories, num_robots, args):
    """
    Improved training function with all performance enhancements.
    """
    device = torch.device(args.device)
    
    # Enhanced network architecture
    in_dim = {'task': 6, 'loc': 1, 'robot': 1, 'state': 4, 'value': 1}
    hid_dim = {'task': 64, 'loc': 64, 'robot': 64, 'state': 64, 'value': 64}
    out_dim = {'task': 32, 'loc': 32, 'robot': 32, 'state': 32, 'value': 1}
    cetypes = [
        ('task', 'temporal', 'task'), ('task', 'located_in', 'loc'), 
        ('loc', 'near', 'loc'), ('task', 'assigned_to', 'robot'), 
        ('robot', 'com', 'robot'), ('task', 'tin', 'state'), 
        ('loc', 'lin', 'state'), ('robot', 'rin', 'state'), 
        ('state', 'sin', 'state'), ('task', 'tto', 'value'), 
        ('robot', 'rto', 'value'), ('state', 'sto', 'value'), 
        ('value', 'vto', 'value'), ('task', 'take_time', 'robot'), 
        ('robot', 'use_time', 'task')
    ]

    # Create improved system
    system = MultiRobotDecentralizedSystem(in_dim, hid_dim, out_dim, cetypes, num_robots, 8).to(device)
    
    # Target network for stable learning
    target_system = copy.deepcopy(system)
    target_update_period = 20  # Update target every 20 steps
    
    # EMA for smoother performance
    ema = AveragedModel(system, avg_fn=lambda avg, new, num_averaged: 0.995*avg + 0.005*new)
    
    # Improved optimizers with higher learning rate
    optimizers = [
        torch.optim.AdamW(
            system.get_robot_network(i).parameters(), 
            lr=2e-4,  # Higher learning rate
            weight_decay=1e-4,  # Reduced weight decay
            eps=1e-8, 
            betas=(0.9, 0.999)
        ) for i in range(num_robots)
    ]
    
    # Better learning rate scheduling
    schedulers = [
        OneCycleLR(
            opt,
            max_lr=2e-4,  # Higher max learning rate
            total_steps=args.steps,
            pct_start=0.15,  # Longer warmup
            div_factor=20,  # Less aggressive initial reduction
            final_div_factor=50  # Moderate final reduction
        ) for opt in optimizers
    ]
    
    # Enhanced loss scaling for better multi-objective learning
    conf_scale = 0.2  # Increased for better learning signal
    balance_scale = 2.0  # High priority for workload balance
    makespan_scale = 1.0  # Moderate makespan optimization
    
    # Training setup
    writer = SummaryWriter(log_dir=args.tbdir)
    metrics = MultiObjectiveMetrics()
    
    print(f"🚀 Starting IMPROVED decentralized training for {args.steps} steps")
    print(f"📊 Enhanced loss scaling: conf={conf_scale}, balance={balance_scale}, makespan={makespan_scale}")
    print(f"🧠 Higher learning rate: {2e-4}, better scheduling, stronger balance focus")
    
    # Training loop
    for step in range(1, args.steps + 1):
        start_time = time.time()
        total_loss = 0.0
        step_rewards = []
        step_makespans = []
        step_balances = []
        
        # Enhanced loss tracking
        loss_components = {
            'conf_loss': 0.0, 'balance_loss': 0.0, 'makespan_penalty': 0.0,
            'exploration': 0.0, 'diversity': 0.0
        }
        
        # Train each robot
        for robot_id in range(num_robots):
            # Enhanced prioritized sampling
            batch = []
            memory = memories[robot_id]
            if len(memory) < 32:
                continue
                
            # Better prioritized sampling with higher probability for good experiences
            priorities = [_calculate_experience_priority(exp) for exp in memory]
            priorities = np.array(priorities)
            
            # Enhanced sampling with temperature
            temperature = max(0.5, 1.0 - step / args.steps)  # Decay temperature
            probs = F.softmax(torch.tensor(priorities) / temperature, dim=0).numpy()
            
            # Sample with replacement for better diversity
            indices = np.random.choice(len(memory), size=min(32, len(memory)), p=probs, replace=True)
            batch = [memory[i] for i in indices]
            
            # Forward pass with enhanced communication
            loss = 0.0
            for t in batch:
                unsch = [tid for tid in range(1, t.durs.shape[0]+1) if tid not in t.curr_partialw]
                if not unsch:
                    continue
                    
                # Build graph and features
                g = build_hetgraph(
                    t.curr_g, t.durs.shape[0], num_robots, t.durs.astype(np.float32), 
                    6, np.array(t.locs, dtype=np.int64), 1, t.curr_partials, 
                    np.array(unsch, dtype=np.int32), robot_id, np.array(unsch, dtype=np.int32)
                ).to(device)
                
                feat = hetgraph_node_helper(
                    t.curr_g.number_of_nodes(), t.curr_partialw, t.curr_partials, 
                    t.locs, t.durs, 6, num_robots, len(unsch)
                )
                feat = {k: torch.tensor(v, device=device, dtype=torch.float32) for k, v in feat.items()}
                
                # Enhanced forward pass with more communication rounds
                out = system.forward_with_communication(robot_id, g, feat, communication_rounds=7)
                q, conf = out['value'], out['confidence']
                
                # Enhanced target calculation using target network
                with torch.no_grad():
                    target_out = target_system.forward_with_communication(robot_id, g, feat, communication_rounds=7)
                    target_q = target_out['value']
                    
                # Better reward shaping for multi-objective learning
                reward_bonus = 0.0
                if t.reward_n > 0:
                    reward_bonus = min(2.0, t.reward_n / 10.0)  # Bonus for good rewards
                    
                targets = torch.full_like(q, t.reward_n + reward_bonus)
                
                # Enhanced loss calculation
                weights_task = torch.ones_like(q)
                
                # Huber loss for robustness
                huber_loss = F.smooth_l1_loss(q, targets, reduction='none')
                conf_loss = (huber_loss * weights_task).mean()
                
                # Enhanced confidence regularization
                conf_reg = torch.mean((conf - 0.7)**2)  # Target higher confidence
                
                # Enhanced workload balance loss
                probs = F.softmax(q.squeeze(), dim=0)
                balance_loss, current_balance_std = _calculate_workload_balance_loss(
                    t, robot_id, probs, num_robots, device, balance_target=0.3
                )
                
                # Enhanced makespan penalty
                estimated_makespan = _estimate_makespan_from_transition(t, device)
                makespan_penalty = torch.clamp(estimated_makespan - 8.0, min=0.0, max=20.0)  # Target makespan < 8
                
                # Enhanced exploration with better scheduling
                entropy = -torch.sum(probs * torch.log(probs + 1e-8))
                exploration_coef = max(0.02, 0.15 * (1 - step / args.steps))
                exploration_bonus = exploration_coef * entropy
                
                # Diversity bonus for different robot strategies
                diversity_bonus = 0.0
                if robot_id > 0:
                    diversity_bonus = 0.1 * torch.var(probs)
                
                # Enhanced total loss
                total_loss_component = (
                    conf_loss * conf_scale +
                    conf_reg * conf_scale * 0.5 +
                    balance_loss * balance_scale +
                    makespan_penalty * makespan_scale -
                    exploration_bonus -
                    diversity_bonus
                )
                
                loss += total_loss_component
                
                # Track components
                loss_components['conf_loss'] += (conf_loss * conf_scale).item()
                loss_components['balance_loss'] += (balance_loss * balance_scale).item()
                loss_components['makespan_penalty'] += (makespan_penalty * makespan_scale).item()
                loss_components['exploration'] += exploration_bonus.item()
                loss_components['diversity'] += diversity_bonus.item()
                
                step_rewards.append(float(t.reward_n))
                if estimated_makespan.item() > 0:
                    step_makespans.append(estimated_makespan.item())
                step_balances.append(current_balance_std)
            
            if loss.item() > 0:
                # Enhanced optimization
                optimizers[robot_id].zero_grad()
                loss.backward()
                
                # Gradient clipping for stability
                clip_grad_norm_(system.get_robot_network(robot_id).parameters(), max_norm=1.0)
                
                optimizers[robot_id].step()
                schedulers[robot_id].step()
                
                # Update EMA
                ema.update_parameters(system)
                
                total_loss += loss.item()
        
        # Update target network periodically
        if step % target_update_period == 0:
            target_system.load_state_dict(system.state_dict())
        
        # Enhanced logging
        runtime = time.time() - start_time
        
        if step % 10 == 0:
            avg_reward = np.mean(step_rewards) if step_rewards else 0.0
            avg_makespan = np.mean(step_makespans) if step_makespans else 0.0
            avg_balance = np.mean(step_balances) if step_balances else 0.0
            
            print(f"Step {step:4d}: loss={total_loss:.3f}, reward={avg_reward:.2f}, "
                  f"makespan={avg_makespan:.1f}, balance={avg_balance:.2f}, time={runtime:.2f}s")
            
            # TensorBoard logging
            writer.add_scalar('Loss/Total', total_loss, step)
            writer.add_scalar('Performance/Reward', avg_reward, step)
            writer.add_scalar('Performance/Makespan', avg_makespan, step)
            writer.add_scalar('Performance/Balance', avg_balance, step)
            
            for component, value in loss_components.items():
                writer.add_scalar(f'Loss/{component}', value, step)
        
        # Save checkpoints
        if step % args.checkpoint_interval == 0:
            checkpoint = {
                'step': step,
                'robot_networks': {f'robot_{i}': system.get_robot_network(i).state_dict() 
                                 for i in range(num_robots)},
                'target_state': target_system.state_dict(),
                'ema_state': ema.state_dict(),
                'optimizers': [opt.state_dict() for opt in optimizers],
                'schedulers': [sch.state_dict() for sch in schedulers],
                'num_robots': num_robots,
                'alpha': args.alpha,
                'beta': args.beta,
                'loss_components': loss_components
            }
            
            checkpoint_path = os.path.join(args.cpsave, f"improved_decentralized_step_{step}.pth")
            os.makedirs(args.cpsave, exist_ok=True)
            torch.save(checkpoint, checkpoint_path)
            print(f"💾 Saved improved checkpoint: {checkpoint_path}")
    
    writer.close()
    print("🎉 Improved decentralized training completed!")


def main():
    parser = argparse.ArgumentParser(description="Improved Decentralized Multi-Objective Training")
    parser.add_argument("--path-to-train", default="./problem_instances/constraints")
    parser.add_argument("--train-start-no", type=int, default=1)
    parser.add_argument("--train-end-no", type=int, default=100)
    parser.add_argument("--num-robots", type=int, default=2)
    parser.add_argument("--alpha", type=float, default=0.3)  # Lower alpha for more balance focus
    parser.add_argument("--beta", type=float, default=0.7)   # Higher beta for balance
    parser.add_argument("--steps", type=int, default=2000)
    parser.add_argument("--checkpoint-interval", type=int, default=100)
    parser.add_argument("--cpsave", default="./cp_improved_decentralized")
    parser.add_argument("--tbdir", default="./runs/ImprovedDecentralized")
    parser.add_argument("--device", default="cpu")
    
    args = parser.parse_args()
    
    print(f"🚀 Improved Decentralized Training: α={args.alpha}, β={args.beta}")
    print(f"📊 Focus: Higher balance weight (β={args.beta}) for better workload distribution")
    
    # Collect training data
    memories = collect_decentralized_data(
        args.path_to_train, args.train_start_no, args.train_end_no, 
        args.num_robots, args.alpha, args.beta
    )
    
    if sum(len(m) for m in memories) == 0:
        print("❌ No training data found. Exiting.")
        return
    
    print(f"📚 Collected {sum(len(m) for m in memories)} training experiences")
    
    # Train improved system
    train_improved_decentralized_system(memories, args.num_robots, args)


if __name__ == "__main__":
    main()

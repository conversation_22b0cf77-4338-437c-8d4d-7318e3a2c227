#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
test_ultra_conservative.py

Test the ultra-conservative training settings to ensure loss < 1 and gradual decrease.
"""

import os
import sys
import subprocess

def test_ultra_conservative_training():
    """Test ultra-conservative training for 5 steps to verify loss < 1."""
    
    print("🧪 Testing ULTRA-CONSERVATIVE training settings...")
    print("🎯 Goal: Loss < 1 and gradually decreasing")
    print("⚙️  Settings:")
    print("   - conf_scale = 0.001")
    print("   - balance_scale = 0.005") 
    print("   - makespan_scale = 0.003")
    print("   - lr = 5e-6")
    print("   - reward normalization: /50.0, clipped to [-0.5, 0.5]")
    print("   - gradient clipping: max_norm=0.01")
    print()
    
    # Run ultra-conservative training
    cmd = [
        "python3", "decentralized_multi_objective_train.py",
        "--steps", "5",
        "--checkpoint-interval", "5", 
        "--train-end-no", "10",
        "--alpha", "0.3",
        "--beta", "0.7"
    ]
    
    print(f"🚀 Running command: {' '.join(cmd)}")
    print("=" * 60)
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
        
        print("📊 TRAINING OUTPUT:")
        print(result.stdout)
        
        if result.stderr:
            print("⚠️  STDERR:")
            print(result.stderr)
        
        # Analyze output for loss values
        lines = result.stdout.split('\n')
        loss_values = []
        
        for line in lines:
            if 'loss=' in line and 'Step' in line:
                try:
                    # Extract loss value
                    loss_part = line.split('loss=')[1].split(',')[0].split()[0]
                    loss_val = float(loss_part)
                    loss_values.append(loss_val)
                    print(f"📈 Found loss: {loss_val}")
                except:
                    continue
        
        print("\n🎯 ANALYSIS:")
        if loss_values:
            print(f"📊 Loss values found: {loss_values}")
            print(f"📉 First loss: {loss_values[0]:.3f}")
            print(f"📉 Last loss: {loss_values[-1]:.3f}")
            
            # Check if loss < 1
            all_under_1 = all(loss < 1.0 for loss in loss_values)
            print(f"✅ All losses < 1.0: {all_under_1}")
            
            # Check if decreasing
            if len(loss_values) > 1:
                decreasing = loss_values[-1] < loss_values[0]
                print(f"📉 Loss decreasing: {decreasing}")
                
                if all_under_1 and decreasing:
                    print("🎉 SUCCESS: Loss < 1 and decreasing!")
                    return True
                elif all_under_1:
                    print("⚠️  PARTIAL: Loss < 1 but not decreasing")
                    return False
                else:
                    print("❌ FAILED: Loss still >= 1")
                    return False
            else:
                if all_under_1:
                    print("⚠️  PARTIAL: Loss < 1 but need more steps to check decrease")
                    return False
                else:
                    print("❌ FAILED: Loss still >= 1")
                    return False
        else:
            print("❌ No loss values found in output")
            return False
            
    except subprocess.TimeoutExpired:
        print("⏰ Training timed out after 5 minutes")
        return False
    except Exception as e:
        print(f"❌ Error running training: {e}")
        return False


def main():
    print("🧪 ULTRA-CONSERVATIVE Training Test")
    print("=" * 50)
    
    # Change to the correct directory
    os.chdir("/home/<USER>/final")
    
    success = test_ultra_conservative_training()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 ULTRA-CONSERVATIVE settings are working!")
        print("✅ Ready for full training with loss < 1")
        print("\n🚀 Next steps:")
        print("   1. Run full training: --steps 1000")
        print("   2. Monitor loss decreasing from ~0.5 to ~0.1")
        print("   3. Test against baselines for better performance")
    else:
        print("⚠️  ULTRA-CONSERVATIVE settings need more tuning")
        print("💡 Suggestions:")
        print("   - Reduce loss scaling even further")
        print("   - Lower learning rate to 1e-6")
        print("   - Increase gradient clipping")
        print("   - Use even more conservative reward normalization")


if __name__ == "__main__":
    main()

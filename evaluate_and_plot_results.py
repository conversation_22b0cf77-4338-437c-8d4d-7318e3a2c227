#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
evaluate_and_plot_results.py

Load results from CSV files, perform evaluation, and generate plots.
"""

import os
import argparse
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from typing import List, Dict, Tuple
import glob


def load_all_results(results_dir: str) -> pd.DataFrame:
    """Load all CSV result files from the directory."""
    csv_files = glob.glob(os.path.join(results_dir, "*_results.csv"))
    
    if not csv_files:
        print(f"No CSV result files found in {results_dir}")
        return pd.DataFrame()
    
    print(f"Found {len(csv_files)} CSV files:")
    for csv_file in csv_files:
        print(f"  - {os.path.basename(csv_file)}")
    
    # Load and combine all results
    all_results = []
    for csv_file in csv_files:
        try:
            df = pd.read_csv(csv_file)
            all_results.append(df)
            print(f"  Loaded {len(df)} records from {os.path.basename(csv_file)}")
        except Exception as e:
            print(f"  Error loading {csv_file}: {e}")
    
    if all_results:
        combined_df = pd.concat(all_results, ignore_index=True)
        print(f"\nCombined dataset: {len(combined_df)} total records")
        return combined_df
    else:
        return pd.DataFrame()


def calculate_summary_statistics(df: pd.DataFrame) -> pd.DataFrame:
    """Calculate summary statistics for each model."""
    if df.empty:
        return pd.DataFrame()
    
    summary_stats = []
    
    for model_name in df['model_name'].unique():
        model_data = df[df['model_name'] == model_name]
        feasible_data = model_data[model_data['feasible'] == True]
        
        stats = {
            'model_name': model_name,
            'model_type': model_data['model_type'].iloc[0] if len(model_data) > 0 else 'unknown',
            'total_instances': len(model_data),
            'feasible_instances': len(feasible_data),
            'feasibility_rate': len(feasible_data) / len(model_data) if len(model_data) > 0 else 0,
            'avg_makespan': feasible_data['makespan'].mean() if len(feasible_data) > 0 else np.nan,
            'std_makespan': feasible_data['makespan'].std() if len(feasible_data) > 0 else np.nan,
            'min_makespan': feasible_data['makespan'].min() if len(feasible_data) > 0 else np.nan,
            'max_makespan': feasible_data['makespan'].max() if len(feasible_data) > 0 else np.nan,
            'avg_workload_balance': feasible_data['workload_balance'].mean() if len(feasible_data) > 0 else np.nan,
            'std_workload_balance': feasible_data['workload_balance'].std() if len(feasible_data) > 0 else np.nan,
            'min_workload_balance': feasible_data['workload_balance'].min() if len(feasible_data) > 0 else np.nan,
            'max_workload_balance': feasible_data['workload_balance'].max() if len(feasible_data) > 0 else np.nan,
            'avg_runtime': feasible_data['runtime'].mean() if len(feasible_data) > 0 else np.nan,
            'std_runtime': feasible_data['runtime'].std() if len(feasible_data) > 0 else np.nan
        }
        
        # Add model-specific parameters
        if 'alpha' in model_data.columns:
            alpha_values = model_data['alpha'].dropna().unique()
            if len(alpha_values) > 0:
                stats['alpha'] = alpha_values[0]
        
        if 'beta' in model_data.columns:
            beta_values = model_data['beta'].dropna().unique()
            if len(beta_values) > 0:
                stats['beta'] = beta_values[0]
        
        if 'strategy' in model_data.columns:
            strategy_values = model_data['strategy'].dropna().unique()
            if len(strategy_values) > 0:
                stats['strategy'] = strategy_values[0]
        
        summary_stats.append(stats)
    
    return pd.DataFrame(summary_stats)


def create_performance_comparison_plots(df: pd.DataFrame, summary_df: pd.DataFrame, output_dir: str):
    """Create comprehensive performance comparison plots."""
    if df.empty or summary_df.empty:
        print("No data available for plotting")
        return
    
    # Set up the plotting style
    plt.style.use('default')
    sns.set_palette("husl")
    
    # Filter to feasible solutions only
    feasible_df = df[df['feasible'] == True].copy()
    
    if feasible_df.empty:
        print("No feasible solutions found for plotting")
        return
    
    # 1. Feasibility Rate Comparison
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('Multi-Objective Model Performance Comparison', fontsize=16, fontweight='bold')
    
    # Feasibility rates
    ax1 = axes[0, 0]
    feasibility_data = summary_df.sort_values('feasibility_rate', ascending=True)
    bars1 = ax1.barh(feasibility_data['model_name'], feasibility_data['feasibility_rate'])
    ax1.set_xlabel('Feasibility Rate')
    ax1.set_title('Feasibility Rate by Model')
    ax1.set_xlim(0, 1)
    
    # Add value labels on bars
    for i, bar in enumerate(bars1):
        width = bar.get_width()
        ax1.text(width + 0.01, bar.get_y() + bar.get_height()/2, 
                f'{width:.2f}', ha='left', va='center', fontsize=9)
    
    # 2. Average Makespan Comparison
    ax2 = axes[0, 1]
    makespan_data = summary_df.dropna(subset=['avg_makespan']).sort_values('avg_makespan')
    bars2 = ax2.bar(range(len(makespan_data)), makespan_data['avg_makespan'], 
                   yerr=makespan_data['std_makespan'], capsize=5)
    ax2.set_xlabel('Model')
    ax2.set_ylabel('Average Makespan')
    ax2.set_title('Average Makespan by Model')
    ax2.set_xticks(range(len(makespan_data)))
    ax2.set_xticklabels(makespan_data['model_name'], rotation=45, ha='right')
    
    # 3. Average Workload Balance Comparison
    ax3 = axes[1, 0]
    balance_data = summary_df.dropna(subset=['avg_workload_balance']).sort_values('avg_workload_balance')
    bars3 = ax3.bar(range(len(balance_data)), balance_data['avg_workload_balance'], 
                   yerr=balance_data['std_workload_balance'], capsize=5)
    ax3.set_xlabel('Model')
    ax3.set_ylabel('Average Workload Balance')
    ax3.set_title('Average Workload Balance by Model (Lower is Better)')
    ax3.set_xticks(range(len(balance_data)))
    ax3.set_xticklabels(balance_data['model_name'], rotation=45, ha='right')
    
    # 4. Runtime Comparison
    ax4 = axes[1, 1]
    runtime_data = summary_df.dropna(subset=['avg_runtime']).sort_values('avg_runtime')
    bars4 = ax4.bar(range(len(runtime_data)), runtime_data['avg_runtime'], 
                   yerr=runtime_data['std_runtime'], capsize=5)
    ax4.set_xlabel('Model')
    ax4.set_ylabel('Average Runtime (seconds)')
    ax4.set_title('Average Runtime by Model')
    ax4.set_xticks(range(len(runtime_data)))
    ax4.set_xticklabels(runtime_data['model_name'], rotation=45, ha='right')
    
    plt.tight_layout()
    
    # Save plot
    plot_path = os.path.join(output_dir, 'performance_comparison.png')
    plt.savefig(plot_path, dpi=300, bbox_inches='tight')
    print(f"Performance comparison plot saved to: {plot_path}")
    plt.show()


def create_pareto_front_plot(df: pd.DataFrame, output_dir: str):
    """Create Pareto front plot for makespan vs workload balance."""
    feasible_df = df[df['feasible'] == True].copy()
    
    if feasible_df.empty:
        print("No feasible solutions for Pareto front plot")
        return
    
    plt.figure(figsize=(12, 8))
    
    # Plot each model with different colors and markers
    models = feasible_df['model_name'].unique()
    colors = plt.cm.Set1(np.linspace(0, 1, len(models)))
    markers = ['o', 's', '^', 'D', 'v', '<', '>', 'p', '*', 'h']
    
    for i, model in enumerate(models):
        model_data = feasible_df[feasible_df['model_name'] == model]
        plt.scatter(model_data['makespan'], model_data['workload_balance'], 
                   c=[colors[i]], marker=markers[i % len(markers)], 
                   s=60, alpha=0.7, label=model, edgecolors='black', linewidth=0.5)
    
    # Calculate and plot Pareto front
    all_points = feasible_df[['makespan', 'workload_balance']].values
    pareto_indices = []
    
    for i, point in enumerate(all_points):
        is_pareto = True
        for j, other_point in enumerate(all_points):
            if i != j:
                # A point is dominated if another point is better in both objectives
                if (other_point[0] <= point[0] and other_point[1] <= point[1] and 
                    (other_point[0] < point[0] or other_point[1] < point[1])):
                    is_pareto = False
                    break
        if is_pareto:
            pareto_indices.append(i)
    
    if pareto_indices:
        pareto_points = all_points[pareto_indices]
        # Sort by makespan for plotting
        sorted_indices = np.argsort(pareto_points[:, 0])
        pareto_points = pareto_points[sorted_indices]
        
        plt.plot(pareto_points[:, 0], pareto_points[:, 1], 'r--', 
                linewidth=2, alpha=0.8, label='Pareto Front')
        
        print(f"Pareto front contains {len(pareto_points)} solutions")
    
    plt.xlabel('Makespan (Lower is Better)')
    plt.ylabel('Workload Balance (Lower is Better)')
    plt.title('Pareto Front: Makespan vs Workload Balance')
    plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
    plt.grid(True, alpha=0.3)
    
    # Save plot
    plot_path = os.path.join(output_dir, 'pareto_front.png')
    plt.savefig(plot_path, dpi=300, bbox_inches='tight')
    print(f"Pareto front plot saved to: {plot_path}")
    plt.show()


def create_detailed_analysis_plots(df: pd.DataFrame, output_dir: str):
    """Create detailed analysis plots."""
    feasible_df = df[df['feasible'] == True].copy()
    
    if feasible_df.empty:
        print("No feasible solutions for detailed analysis")
        return
    
    # 1. Box plots for makespan and workload balance
    fig, axes = plt.subplots(1, 2, figsize=(15, 6))
    
    # Makespan box plot
    ax1 = axes[0]
    makespan_data = [feasible_df[feasible_df['model_name'] == model]['makespan'].values 
                    for model in feasible_df['model_name'].unique()]
    model_names = feasible_df['model_name'].unique()
    
    bp1 = ax1.boxplot(makespan_data, labels=model_names, patch_artist=True)
    ax1.set_title('Makespan Distribution by Model')
    ax1.set_ylabel('Makespan')
    ax1.tick_params(axis='x', rotation=45)
    
    # Color the boxes
    colors = plt.cm.Set3(np.linspace(0, 1, len(bp1['boxes'])))
    for patch, color in zip(bp1['boxes'], colors):
        patch.set_facecolor(color)
    
    # Workload balance box plot
    ax2 = axes[1]
    balance_data = [feasible_df[feasible_df['model_name'] == model]['workload_balance'].values 
                   for model in feasible_df['model_name'].unique()]
    
    bp2 = ax2.boxplot(balance_data, labels=model_names, patch_artist=True)
    ax2.set_title('Workload Balance Distribution by Model')
    ax2.set_ylabel('Workload Balance')
    ax2.tick_params(axis='x', rotation=45)
    
    # Color the boxes
    for patch, color in zip(bp2['boxes'], colors):
        patch.set_facecolor(color)
    
    plt.tight_layout()
    
    # Save plot
    plot_path = os.path.join(output_dir, 'distribution_analysis.png')
    plt.savefig(plot_path, dpi=300, bbox_inches='tight')
    print(f"Distribution analysis plot saved to: {plot_path}")
    plt.show()


def generate_detailed_report(df: pd.DataFrame, summary_df: pd.DataFrame, output_dir: str):
    """Generate a detailed text report."""
    report_path = os.path.join(output_dir, 'evaluation_report.txt')
    
    with open(report_path, 'w') as f:
        f.write("MULTI-OBJECTIVE MRC SCHEDULING EVALUATION REPORT\n")
        f.write("=" * 60 + "\n\n")
        
        # Overall statistics
        f.write("OVERALL STATISTICS\n")
        f.write("-" * 30 + "\n")
        f.write(f"Total instances tested: {len(df['instance_id'].unique())}\n")
        f.write(f"Total models evaluated: {len(df['model_name'].unique())}\n")
        f.write(f"Total evaluations: {len(df)}\n")
        f.write(f"Overall feasibility rate: {(df['feasible'].sum() / len(df)):.3f}\n\n")
        
        # Model-by-model analysis
        f.write("MODEL PERFORMANCE SUMMARY\n")
        f.write("-" * 30 + "\n")
        
        for _, row in summary_df.iterrows():
            f.write(f"\nModel: {row['model_name']}\n")
            f.write(f"  Type: {row['model_type']}\n")
            
            if 'alpha' in row and not pd.isna(row['alpha']):
                f.write(f"  Alpha (makespan weight): {row['alpha']:.3f}\n")
            if 'beta' in row and not pd.isna(row['beta']):
                f.write(f"  Beta (balance weight): {row['beta']:.3f}\n")
            if 'strategy' in row and not pd.isna(row['strategy']):
                f.write(f"  Strategy: {row['strategy']}\n")
            
            f.write(f"  Feasibility: {row['feasible_instances']}/{row['total_instances']} ({row['feasibility_rate']:.3f})\n")
            
            if row['feasible_instances'] > 0:
                f.write(f"  Makespan: {row['avg_makespan']:.2f} ± {row['std_makespan']:.2f} (range: {row['min_makespan']:.2f}-{row['max_makespan']:.2f})\n")
                f.write(f"  Workload Balance: {row['avg_workload_balance']:.3f} ± {row['std_workload_balance']:.3f} (range: {row['min_workload_balance']:.3f}-{row['max_workload_balance']:.3f})\n")
                f.write(f"  Runtime: {row['avg_runtime']:.3f} ± {row['std_runtime']:.3f} seconds\n")
            else:
                f.write("  No feasible solutions found\n")
        
        # Best performers
        f.write("\n\nBEST PERFORMERS\n")
        f.write("-" * 30 + "\n")
        
        feasible_models = summary_df[summary_df['feasible_instances'] > 0]
        
        if not feasible_models.empty:
            best_makespan = feasible_models.loc[feasible_models['avg_makespan'].idxmin()]
            best_balance = feasible_models.loc[feasible_models['avg_workload_balance'].idxmin()]
            best_feasibility = feasible_models.loc[feasible_models['feasibility_rate'].idxmax()]
            
            f.write(f"Best Makespan: {best_makespan['model_name']} ({best_makespan['avg_makespan']:.2f})\n")
            f.write(f"Best Workload Balance: {best_balance['model_name']} ({best_balance['avg_workload_balance']:.3f})\n")
            f.write(f"Best Feasibility: {best_feasibility['model_name']} ({best_feasibility['feasibility_rate']:.3f})\n")
        
        # Recommendations
        f.write("\n\nRECOMMENDATIONS\n")
        f.write("-" * 30 + "\n")
        
        multi_obj_models = summary_df[summary_df['model_type'] == 'multi_objective']
        baseline_models = summary_df[summary_df['model_type'] == 'baseline']
        
        if not multi_obj_models.empty and not baseline_models.empty:
            avg_mo_makespan = multi_obj_models['avg_makespan'].mean()
            avg_baseline_makespan = baseline_models['avg_makespan'].mean()
            avg_mo_balance = multi_obj_models['avg_workload_balance'].mean()
            avg_baseline_balance = baseline_models['avg_workload_balance'].mean()
            
            f.write(f"Multi-objective models vs Baselines:\n")
            f.write(f"  Makespan: {avg_mo_makespan:.2f} vs {avg_baseline_makespan:.2f} ({'better' if avg_mo_makespan < avg_baseline_makespan else 'worse'})\n")
            f.write(f"  Balance: {avg_mo_balance:.3f} vs {avg_baseline_balance:.3f} ({'better' if avg_mo_balance < avg_baseline_balance else 'worse'})\n")
    
    print(f"Detailed report saved to: {report_path}")


def main():
    parser = argparse.ArgumentParser(description="Evaluate and plot results from individual model testing")
    parser.add_argument("--results-dir", default="./individual_results", 
                       help="Directory containing CSV result files")
    parser.add_argument("--output-dir", help="Output directory for plots and analysis (default: same as results-dir)")
    
    args = parser.parse_args()
    
    if args.output_dir is None:
        args.output_dir = args.results_dir
    
    os.makedirs(args.output_dir, exist_ok=True)
    
    print("Multi-Objective Results Evaluation and Plotting")
    print("=" * 50)
    print(f"Results directory: {args.results_dir}")
    print(f"Output directory: {args.output_dir}")
    
    # Load all results
    df = load_all_results(args.results_dir)
    
    if df.empty:
        print("No data found. Please run test_individual_models.py first.")
        return
    
    # Calculate summary statistics
    summary_df = calculate_summary_statistics(df)
    
    # Save summary statistics
    summary_path = os.path.join(args.output_dir, 'summary_statistics.csv')
    summary_df.to_csv(summary_path, index=False)
    print(f"\nSummary statistics saved to: {summary_path}")
    
    # Display summary
    print("\nSUMMARY STATISTICS:")
    print(summary_df[['model_name', 'feasibility_rate', 'avg_makespan', 'avg_workload_balance']].to_string(index=False, float_format='%.3f'))
    
    # Generate plots
    print("\nGenerating plots...")
    create_performance_comparison_plots(df, summary_df, args.output_dir)
    create_pareto_front_plot(df, args.output_dir)
    create_detailed_analysis_plots(df, args.output_dir)
    
    # Generate detailed report
    generate_detailed_report(df, summary_df, args.output_dir)
    
    print("\n=== EVALUATION COMPLETED ===")
    print(f"All results saved to: {args.output_dir}")


if __name__ == "__main__":
    main()

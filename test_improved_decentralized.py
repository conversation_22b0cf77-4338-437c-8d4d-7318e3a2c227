#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
test_improved_decentralized.py

Test script for the improved decentralized model with comprehensive evaluation.
Compares against baseline solvers (EDF, Tercio) to demonstrate improvements.
"""

import os
import sys
import argparse
import time
import numpy as np
import pandas as pd
import torch
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, List, Tuple

from utils import SchedulingEnv, build_hetgraph, hetgraph_node_helper
from hetnet import MultiRobotDecentralizedSystem
from multi_objective_utils import calculate_workload_balance


def load_improved_model(checkpoint_path: str, device: torch.device):
    """Load the improved decentralized model from checkpoint."""
    checkpoint = torch.load(checkpoint_path, map_location=device)
    
    # Network architecture
    in_dim = {'task': 6, 'loc': 1, 'robot': 1, 'state': 4, 'value': 1}
    hid_dim = {'task': 64, 'loc': 64, 'robot': 64, 'state': 64, 'value': 64}
    out_dim = {'task': 32, 'loc': 32, 'robot': 32, 'state': 32, 'value': 1}
    cetypes = [
        ('task', 'temporal', 'task'), ('task', 'located_in', 'loc'), 
        ('loc', 'near', 'loc'), ('task', 'assigned_to', 'robot'), 
        ('robot', 'com', 'robot'), ('task', 'tin', 'state'), 
        ('loc', 'lin', 'state'), ('robot', 'rin', 'state'), 
        ('state', 'sin', 'state'), ('task', 'tto', 'value'), 
        ('robot', 'rto', 'value'), ('state', 'sto', 'value'), 
        ('value', 'vto', 'value'), ('task', 'take_time', 'robot'), 
        ('robot', 'use_time', 'task')
    ]
    
    num_robots = checkpoint['num_robots']
    alpha = checkpoint.get('alpha', 0.3)
    beta = checkpoint.get('beta', 0.7)
    step = checkpoint.get('step', 0)
    
    # Create system
    system = MultiRobotDecentralizedSystem(in_dim, hid_dim, out_dim, cetypes, num_robots, 8).to(device)
    
    # Load robot network states
    robot_networks = checkpoint['robot_networks']
    for robot_id in range(num_robots):
        robot_key = f'robot_{robot_id}'
        if robot_key in robot_networks:
            robot_net = system.get_robot_network(robot_id)
            robot_net.load_state_dict(robot_networks[robot_key])
            robot_net.eval()
    
    return system, alpha, beta, step


def solve_with_improved_decentralized(prefix: str, num_tasks: int, num_robots: int,
                                    system, device: torch.device,
                                    alpha: float = 0.3, beta: float = 0.7,
                                    communication_rounds: int = 7) -> Tuple[float, float, Dict, float, bool]:
    """Solve using improved decentralized model."""
    t0 = time.time()
    
    try:
        env = SchedulingEnv(prefix)
        env.set_multi_objective_params(alpha=alpha, beta=beta)
        ok, mm = env.check_consistency_makespan(updateDG=False)
        if not ok:
            return float("nan"), float("nan"), {}, time.time() - t0, False
        env.min_makespan = mm
    except Exception:
        return float("nan"), float("nan"), {}, time.time() - t0, False
    
    assignments = {r: [] for r in range(num_robots)}
    feasible_flag = True
    
    # Enhanced parameters
    max_coord = max(np.max(env.loc[:, 0]), np.max(env.loc[:, 1])) if len(env.loc) > 0 else 6
    map_width = max(6, max_coord + 2)
    loc_dist_threshold = max(1, map_width // 4)
    
    step_count = 0
    while True:
        unsch_tasks = env.get_unscheduled_tasks()
        if len(unsch_tasks) == 0:
            break
        
        step_count += 1
        if step_count > num_tasks * 3:  # Increased safety margin
            feasible_flag = False
            break
        
        # Enhanced decentralized decision making
        robot_decisions = {}
        robot_confidences = {}
        
        # Each robot makes enhanced local decisions
        for robot_id in range(num_robots):
            try:
                g = build_hetgraph(
                    env.halfDG, num_tasks, num_robots, env.dur.astype(np.float32),
                    map_width, np.array(env.loc, dtype=np.int64), loc_dist_threshold,
                    env.partials, np.array(unsch_tasks, dtype=np.int64),
                    robot_id, np.array(unsch_tasks, dtype=np.int64)
                ).to(device)
                
                feat_dict = hetgraph_node_helper(
                    env.halfDG.number_of_nodes(), env.partialw, env.partials,
                    env.loc, env.dur, map_width, num_robots, len(unsch_tasks)
                )
                
                feat_tensors = {k: torch.tensor(v, device=device, dtype=torch.float32) 
                               for k, v in feat_dict.items()}
                
                # Enhanced forward pass with more communication
                with torch.no_grad():
                    outputs = system.forward_with_communication(
                        robot_id, g, feat_tensors, communication_rounds=communication_rounds
                    )
                    q_values = outputs['value'].cpu().numpy().reshape(-1)
                    confidence = outputs['confidence'].cpu().numpy().reshape(-1)
                
                # Enhanced task selection with confidence weighting
                confidence_weighted_q = q_values * confidence.reshape(-1)
                best_idx = np.argmax(confidence_weighted_q)
                best_task = int(unsch_tasks[best_idx])
                best_q = float(confidence_weighted_q[best_idx])
                best_conf = float(confidence[best_idx])
                
                robot_decisions[robot_id] = {
                    'task': best_task,
                    'q_value': best_q,
                    'confidence': best_conf,
                    'raw_q': float(q_values[best_idx])
                }
                robot_confidences[robot_id] = best_conf
                
            except Exception:
                robot_decisions[robot_id] = {
                    'task': None, 'q_value': -float('inf'), 'confidence': 0.0, 'raw_q': -float('inf')
                }
                robot_confidences[robot_id] = 0.0
        
        # Enhanced conflict resolution with multi-criteria selection
        best_robot = None
        best_task = None
        best_score = -float('inf')
        
        for robot_id, decision in robot_decisions.items():
            if decision['task'] is not None:
                # Multi-criteria score: confidence + q_value + workload balance
                current_load = len(assignments[robot_id])
                avg_load = sum(len(assignments[r]) for r in range(num_robots)) / num_robots
                balance_bonus = max(0, avg_load - current_load) * 0.1  # Bonus for less loaded robots
                
                total_score = decision['confidence'] * 0.6 + decision['raw_q'] * 0.3 + balance_bonus * 0.1
                
                if total_score > best_score:
                    best_score = total_score
                    best_robot = robot_id
                    best_task = decision['task']
        
        if best_robot is None or best_task is None:
            feasible_flag = False
            break
        
        # Execute best action
        success, _, done_flag = env.insert_robot(best_task, best_robot)
        if not success:
            feasible_flag = False
            break
        
        assignments[best_robot].append(best_task)
        
        if done_flag:
            break
    
    runtime = time.time() - t0
    
    if not feasible_flag:
        return float("nan"), float("nan"), assignments, runtime, False
    
    # Calculate final metrics
    try:
        ok_final, final_makespan = env.check_consistency_makespan(updateDG=False)
        if not ok_final:
            return float("nan"), float("nan"), assignments, runtime, False
        
        workload_balance = calculate_workload_balance(assignments, num_robots)
        
        return final_makespan, workload_balance, assignments, runtime, True
        
    except Exception:
        return float("nan"), float("nan"), assignments, runtime, False


def run_comprehensive_evaluation(model_path: str, test_data_path: str, 
                                num_tasks: int, num_robots: int, max_instances: int,
                                output_dir: str, device: torch.device):
    """Run comprehensive evaluation comparing improved model with baselines."""
    
    print("🚀 COMPREHENSIVE EVALUATION: Improved Decentralized vs Baselines")
    print("=" * 70)
    
    # Load improved model
    try:
        system, alpha, beta, step = load_improved_model(model_path, device)
        print(f"✅ Loaded improved model: α={alpha:.3f}, β={beta:.3f}, step={step}")
    except Exception as e:
        print(f"❌ Failed to load model: {e}")
        return
    
    # Results storage
    results = {
        'improved_decentralized': {'makespans': [], 'balances': [], 'feasible': [], 'runtimes': []},
        'edf_baseline': {'makespans': [], 'balances': [], 'feasible': [], 'runtimes': []},
        'tercio_baseline': {'makespans': [], 'balances': [], 'feasible': [], 'runtimes': []}
    }
    
    successful_instances = 0
    total_instances = 0
    
    print(f"📊 Testing on up to {max_instances} instances...")
    
    for inst_id in range(1, max_instances + 1):
        prefix = os.path.join(test_data_path, f"{inst_id:05d}")
        
        if not os.path.isfile(f"{prefix}_dur.txt"):
            continue
        
        total_instances += 1
        print(f"\n[Instance {inst_id:05d}] Testing all methods...")
        
        # Test improved decentralized model
        mk_dec, bal_dec, assign_dec, rt_dec, ok_dec = solve_with_improved_decentralized(
            prefix, num_tasks, num_robots, system, device, alpha, beta
        )
        
        results['improved_decentralized']['makespans'].append(mk_dec)
        results['improved_decentralized']['balances'].append(bal_dec)
        results['improved_decentralized']['feasible'].append(ok_dec)
        results['improved_decentralized']['runtimes'].append(rt_dec)
        
        # Test EDF baseline (import from baselines.py)
        try:
            from baselines import solve_with_edf
            mk_edf, bal_edf, assign_edf, rt_edf, ok_edf = solve_with_edf(prefix, num_tasks, num_robots)
        except:
            mk_edf, bal_edf, rt_edf, ok_edf = float('nan'), float('nan'), 0.0, False
        
        results['edf_baseline']['makespans'].append(mk_edf)
        results['edf_baseline']['balances'].append(bal_edf)
        results['edf_baseline']['feasible'].append(ok_edf)
        results['edf_baseline']['runtimes'].append(rt_edf)
        
        # Test Tercio baseline
        try:
            from baselines import solve_with_tercio
            mk_ter, bal_ter, assign_ter, rt_ter, ok_ter = solve_with_tercio(prefix, num_tasks, num_robots)
        except:
            mk_ter, bal_ter, rt_ter, ok_ter = float('nan'), float('nan'), 0.0, False
        
        results['tercio_baseline']['makespans'].append(mk_ter)
        results['tercio_baseline']['balances'].append(bal_ter)
        results['tercio_baseline']['feasible'].append(ok_ter)
        results['tercio_baseline']['runtimes'].append(rt_ter)
        
        # Print instance results
        if ok_dec:
            print(f"  🤖 Improved: makespan={mk_dec:.1f}, balance={bal_dec:.2f} ✅")
        else:
            print(f"  🤖 Improved: INFEASIBLE ❌")
            
        if ok_edf:
            print(f"  📊 EDF: makespan={mk_edf:.1f}, balance={bal_edf:.2f} ✅")
        else:
            print(f"  📊 EDF: INFEASIBLE ❌")
            
        if ok_ter:
            print(f"  🎯 Tercio: makespan={mk_ter:.1f}, balance={bal_ter:.2f} ✅")
        else:
            print(f"  🎯 Tercio: INFEASIBLE ❌")
        
        if ok_dec:
            successful_instances += 1
    
    # Generate comprehensive analysis
    print(f"\n📈 COMPREHENSIVE ANALYSIS")
    print("=" * 50)
    
    for method_name, method_results in results.items():
        feasible_count = sum(method_results['feasible'])
        success_rate = (feasible_count / total_instances) * 100 if total_instances > 0 else 0
        
        print(f"\n{method_name.replace('_', ' ').title()}:")
        print(f"  Success Rate: {feasible_count}/{total_instances} ({success_rate:.1f}%)")
        
        if feasible_count > 0:
            feasible_makespans = [mk for mk, feas in zip(method_results['makespans'], method_results['feasible']) if feas]
            feasible_balances = [bal for bal, feas in zip(method_results['balances'], method_results['feasible']) if feas]
            feasible_runtimes = [rt for rt, feas in zip(method_results['runtimes'], method_results['feasible']) if feas]
            
            print(f"  Avg Makespan: {np.mean(feasible_makespans):.2f} ± {np.std(feasible_makespans):.2f}")
            print(f"  Avg Balance: {np.mean(feasible_balances):.3f} ± {np.std(feasible_balances):.3f}")
            print(f"  Avg Runtime: {np.mean(feasible_runtimes):.3f}s")
    
    # Save results to CSV
    os.makedirs(output_dir, exist_ok=True)
    
    for method_name, method_results in results.items():
        # Makespan CSV
        makespan_df = pd.DataFrame({
            'instance_id': [f"{i:05d}" for i in range(1, len(method_results['makespans']) + 1)],
            'makespan': method_results['makespans'],
            'feasible': [1 if f else 0 for f in method_results['feasible']],
            'runtime': method_results['runtimes']
        })
        makespan_path = os.path.join(output_dir, f"makespans_{method_name}.csv")
        makespan_df.to_csv(makespan_path, index=False)
        
        # Balance CSV
        balance_df = pd.DataFrame({
            'instance_id': [f"{i:05d}" for i in range(1, len(method_results['balances']) + 1)],
            'workload_balance': method_results['balances'],
            'feasible': [1 if f else 0 for f in method_results['feasible']],
            'runtime': method_results['runtimes']
        })
        balance_path = os.path.join(output_dir, f"workload_balance_{method_name}.csv")
        balance_df.to_csv(balance_path, index=False)
        
        print(f"💾 Saved {method_name} results to {output_dir}")
    
    print(f"\n🎉 Comprehensive evaluation completed!")
    print(f"📁 Results saved to: {output_dir}")


def main():
    parser = argparse.ArgumentParser(description="Test Improved Decentralized Model")
    parser.add_argument("--model-path", required=True, help="Path to improved model checkpoint")
    parser.add_argument("--test-data", default="./problem_instances/constraints", help="Test data path")
    parser.add_argument("--num-tasks", type=int, default=5, help="Number of tasks")
    parser.add_argument("--num-robots", type=int, default=2, help="Number of robots")
    parser.add_argument("--max-instances", type=int, default=100, help="Max instances to test")
    parser.add_argument("--output-dir", default="./results_improved_decentralized", help="Output directory")
    parser.add_argument("--device", default="cpu", help="Device (cpu or cuda)")
    
    args = parser.parse_args()
    
    device = torch.device(args.device)
    
    run_comprehensive_evaluation(
        args.model_path, args.test_data, args.num_tasks, args.num_robots,
        args.max_instances, args.output_dir, device
    )


if __name__ == "__main__":
    main()

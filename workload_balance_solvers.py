#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
workload_balance_solvers.py

Solvers for workload balance optimization in MRC scheduling.
Includes both pure workload balance optimization and multi-objective approaches.
"""

import time
import numpy as np
import gurobipy as gp
from gurobipy import GRB
from typing import Dict, List, Tuple, Optional
import copy

from utils import SchedulingEnv
from multi_objective_utils import calculate_workload_balance, calculate_weighted_objective


def solve_workload_balance_gurobi(prefix: str, num_tasks: int, num_robots: int) -> Tuple[float, Dict, float, bool]:
    """
    Solve for optimal workload balance using Gurobi MILP.
    Primary objective: minimize workload imbalance (variance in task counts)
    Secondary objective: minimize makespan
    
    Args:
        prefix: Path prefix for constraint files
        num_tasks: Number of tasks
        num_robots: Number of robots
    
    Returns:
        Tuple of (workload_balance_score, assignments, runtime, feasible_flag)
    """
    t0 = time.time()
    
    try:
        env = SchedulingEnv(prefix)
    except:
        return float("nan"), {}, time.time() - t0, False
    
    tasks = list(range(1, num_tasks + 1))
    robots = list(range(num_robots))
    
    model = gp.Model("workload_balance")
    model.setParam("OutputFlag", 0)
    
    # Decision variables
    start_times = model.addVars(tasks, lb=0, vtype=GRB.CONTINUOUS, name="start")
    x = model.addVars(tasks, robots, vtype=GRB.BINARY, name="assign")
    makespan = model.addVar(lb=0, vtype=GRB.CONTINUOUS, name="makespan")
    
    # Workload balance variables
    task_count = model.addVars(robots, lb=0, vtype=GRB.INTEGER, name="task_count")
    avg_tasks = model.addVar(lb=0, vtype=GRB.CONTINUOUS, name="avg_tasks")
    max_deviation = model.addVar(lb=0, vtype=GRB.CONTINUOUS, name="max_dev")
    
    # Primary objective: minimize maximum deviation from average workload
    model.setObjective(max_deviation, GRB.MINIMIZE)
    
    # Each task assigned exactly once
    for t in tasks:
        model.addConstr(gp.quicksum(x[t, r] for r in robots) == 1, f"assign_{t}")
    
    # Count tasks per robot
    for r in robots:
        model.addConstr(task_count[r] == gp.quicksum(x[t, r] for t in tasks), f"count_{r}")
    
    # Average tasks per robot
    model.addConstr(avg_tasks == gp.quicksum(task_count[r] for r in robots) / num_robots, "avg_tasks")
    
    # Maximum deviation constraint
    for r in robots:
        model.addConstr(max_deviation >= task_count[r] - avg_tasks, f"dev_pos_{r}")
        model.addConstr(max_deviation >= avg_tasks - task_count[r], f"dev_neg_{r}")
    
    # Duration constraints (for feasibility)
    for t in tasks:
        model.addConstr(
            start_times[t] + gp.quicksum(x[t, r] * env.dur[t - 1, r] for r in robots) <= makespan,
            f"dur_{t}"
        )
    
    # Deadline constraints
    for (tid, dl) in env.ddl:
        if tid in tasks:
            model.addConstr(start_times[tid] <= dl, f"deadline_{tid}")
    
    # Waiting constraints
    for (ti, tj, wt) in env.wait:
        model.addConstr(start_times[ti] + wt <= start_times[tj], f"wait_{ti}_{tj}")
    
    model.optimize()
    
    if model.status != GRB.OPTIMAL:
        return float("nan"), {}, time.time() - t0, False
    
    # Extract assignments
    assignments = {r: [] for r in robots}
    for t in tasks:
        for r in robots:
            if x[t, r].X > 0.5:
                assignments[r].append(t)
                break
    
    # Calculate workload balance score
    balance_score = calculate_workload_balance(assignments, num_robots)
    
    runtime = time.time() - t0
    return balance_score, assignments, runtime, True


def solve_multi_objective_gurobi(prefix: str, num_tasks: int, num_robots: int, 
                                alpha: float = 0.5, beta: float = 0.5) -> Tuple[float, float, Dict, float, bool]:
    """
    Solve multi-objective optimization: makespan + workload balance using Gurobi.
    
    Args:
        prefix: Path prefix for constraint files
        num_tasks: Number of tasks
        num_robots: Number of robots
        alpha: Weight for makespan objective (0-1)
        beta: Weight for workload balance objective (0-1)
    
    Returns:
        Tuple of (makespan, workload_balance, assignments, runtime, feasible_flag)
    """
    t0 = time.time()
    
    try:
        env = SchedulingEnv(prefix)
    except:
        return float("nan"), float("nan"), {}, time.time() - t0, False
    
    tasks = list(range(1, num_tasks + 1))
    robots = list(range(num_robots))
    
    model = gp.Model("multi_objective")
    model.setParam("OutputFlag", 0)
    
    # Decision variables
    start_times = model.addVars(tasks, lb=0, vtype=GRB.CONTINUOUS, name="start")
    x = model.addVars(tasks, robots, vtype=GRB.BINARY, name="assign")
    makespan = model.addVar(lb=0, vtype=GRB.CONTINUOUS, name="makespan")
    
    # Workload balance variables
    task_count = model.addVars(robots, lb=0, vtype=GRB.INTEGER, name="task_count")
    avg_tasks = model.addVar(lb=0, vtype=GRB.CONTINUOUS, name="avg_tasks")
    max_deviation = model.addVar(lb=0, vtype=GRB.CONTINUOUS, name="max_dev")
    
    # Multi-objective: weighted sum of normalized makespan and workload balance
    # Estimate normalization factors (rough approximation)
    makespan_norm = num_tasks * 10.0  # Rough upper bound
    balance_norm = num_tasks / 2.0    # Rough upper bound for balance
    
    model.setObjective(
        alpha * (makespan / makespan_norm) + beta * (max_deviation / balance_norm),
        GRB.MINIMIZE
    )
    
    # Each task assigned exactly once
    for t in tasks:
        model.addConstr(gp.quicksum(x[t, r] for r in robots) == 1, f"assign_{t}")
    
    # Count tasks per robot
    for r in robots:
        model.addConstr(task_count[r] == gp.quicksum(x[t, r] for t in tasks), f"count_{r}")
    
    # Average tasks per robot
    model.addConstr(avg_tasks == gp.quicksum(task_count[r] for r in robots) / num_robots, "avg_tasks")
    
    # Maximum deviation constraint
    for r in robots:
        model.addConstr(max_deviation >= task_count[r] - avg_tasks, f"dev_pos_{r}")
        model.addConstr(max_deviation >= avg_tasks - task_count[r], f"dev_neg_{r}")
    
    # Duration constraints
    for t in tasks:
        model.addConstr(
            start_times[t] + gp.quicksum(x[t, r] * env.dur[t - 1, r] for r in robots) <= makespan,
            f"dur_{t}"
        )
    
    # Deadline constraints
    for (tid, dl) in env.ddl:
        if tid in tasks:
            model.addConstr(start_times[tid] <= dl, f"deadline_{tid}")
    
    # Waiting constraints
    for (ti, tj, wt) in env.wait:
        model.addConstr(start_times[ti] + wt <= start_times[tj], f"wait_{ti}_{tj}")
    
    model.optimize()
    
    if model.status != GRB.OPTIMAL:
        return float("nan"), float("nan"), {}, time.time() - t0, False
    
    # Extract assignments
    assignments = {r: [] for r in robots}
    for t in tasks:
        for r in robots:
            if x[t, r].X > 0.5:
                assignments[r].append(t)
                break
    
    # Sort by start time
    for r in robots:
        assignments[r].sort(key=lambda task: start_times[task].X)
    
    final_makespan = model.objVal if alpha == 1.0 and beta == 0.0 else makespan.X
    balance_score = calculate_workload_balance(assignments, num_robots)
    
    runtime = time.time() - t0
    return final_makespan, balance_score, assignments, runtime, True


def solve_workload_balance_heuristic(prefix: str, num_tasks: int, num_robots: int) -> Tuple[float, Dict, float, bool]:
    """
    Heuristic solver for workload balance: Round-robin task assignment with feasibility checking.
    
    Args:
        prefix: Path prefix for constraint files
        num_tasks: Number of tasks
        num_robots: Number of robots
    
    Returns:
        Tuple of (workload_balance_score, assignments, runtime, feasible_flag)
    """
    t0 = time.time()
    
    try:
        env = SchedulingEnv(prefix)
        ok, mm = env.check_consistency_makespan(updateDG=False)
        if not ok:
            return float("nan"), {}, time.time() - t0, False
        env.min_makespan = mm
    except:
        return float("nan"), {}, time.time() - t0, False
    
    # Round-robin assignment strategy
    robot_task_counts = [0] * num_robots
    assignments = {r: [] for r in range(num_robots)}
    
    # Get all tasks and sort by some criteria (e.g., task ID)
    all_tasks = list(range(1, num_tasks + 1))
    
    for task in all_tasks:
        # Find robot with minimum current task count
        min_count = min(robot_task_counts)
        candidate_robots = [r for r in range(num_robots) if robot_task_counts[r] == min_count]
        
        # Among candidates, try to find one that maintains feasibility
        assigned = False
        for robot in candidate_robots:
            # Test feasibility by creating a copy and trying insertion
            test_env = copy.deepcopy(env)
            success, _, _ = test_env.insert_robot(task, robot)
            
            if success:
                # Actually assign to the real environment
                success2, _, _ = env.insert_robot(task, robot)
                if success2:
                    assignments[robot].append(task)
                    robot_task_counts[robot] += 1
                    assigned = True
                    break
        
        if not assigned:
            # If no feasible assignment found, return failure
            return float("nan"), {}, time.time() - t0, False
    
    # Calculate workload balance score
    balance_score = calculate_workload_balance(assignments, num_robots)
    
    runtime = time.time() - t0
    return balance_score, assignments, runtime, True

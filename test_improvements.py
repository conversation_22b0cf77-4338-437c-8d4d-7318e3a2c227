#!/usr/bin/env python3
"""
Test script to validate the three key improvements:
1. Re-scaled loss terms with proper scaling factors
2. Improved convergence with LambdaLR scheduler and gradient clipping
3. Loosened reward normalization for better learning
"""

import torch
import torch.nn.functional as F
from torch.optim.lr_scheduler import LambdaLR
from torch.nn.utils import clip_grad_norm_
import numpy as np
import math
import matplotlib.pyplot as plt

def test_loss_scaling():
    """Test the new loss scaling factors"""
    print("🧪 Testing Loss Scaling Factors...")
    
    # Simulate loss components
    conf_loss = torch.tensor(2.5)
    conf_reg = torch.tensor(0.8)
    balance_loss = torch.tensor(1.2)
    makespan_penalty = torch.tensor(3.0)
    exploration_bonus = torch.tensor(0.1)
    
    # New scaling factors
    conf_scale = 0.1
    balance_scale = 1.0
    makespan_scale = 1.0
    
    # Old calculation (with hard-coded multipliers)
    old_total = conf_loss + conf_reg * 0.01 + balance_loss * 0.3 + makespan_penalty * 0.1 - exploration_bonus
    
    # New calculation (with scaling factors)
    new_total = (
        conf_loss * conf_scale +
        conf_reg * conf_scale +
        balance_loss * balance_scale +
        makespan_penalty * makespan_scale -
        exploration_bonus
    )
    
    print(f"  Confidence loss: {conf_loss.item():.3f} * {conf_scale} = {(conf_loss * conf_scale).item():.3f}")
    print(f"  Confidence reg:  {conf_reg.item():.3f} * {conf_scale} = {(conf_reg * conf_scale).item():.3f}")
    print(f"  Balance loss:    {balance_loss.item():.3f} * {balance_scale} = {(balance_loss * balance_scale).item():.3f}")
    print(f"  Makespan penalty: {makespan_penalty.item():.3f} * {makespan_scale} = {(makespan_penalty * makespan_scale).item():.3f}")
    print(f"  Old total loss: {old_total.item():.4f}")
    print(f"  New total loss: {new_total.item():.4f}")
    print(f"  Scaling effect: {'✅ More balanced' if abs(new_total.item()) < abs(old_total.item()) else '❌ Less balanced'}")
    print()


def test_lr_scheduler():
    """Test the new LambdaLR scheduler with warmup and cosine decay"""
    print("🧪 Testing LambdaLR Scheduler...")
    
    # Simulate model parameters
    model = torch.nn.Linear(10, 1)
    optimizer = torch.optim.Adam(model.parameters(), lr=1e-4, weight_decay=1e-4)
    
    total_steps = 1000
    
    # Define the lr_lambda function
    def lr_lambda(step):
        if step < 100:  # Shorter warmup for testing
            return step / 100
        return 0.5 * (1 + math.cos((step - 100) / (total_steps - 100) * math.pi))
    
    scheduler = LambdaLR(optimizer, lr_lambda)
    
    # Test learning rate schedule
    steps = []
    learning_rates = []
    
    for step in range(0, total_steps, 50):
        # Simulate scheduler steps
        for _ in range(50):
            scheduler.step()
        
        current_lr = optimizer.param_groups[0]['lr']
        steps.append(step)
        learning_rates.append(current_lr)
        
        if step in [0, 100, 500, 999]:
            print(f"  Step {step:4d}: LR = {current_lr:.2e}")
    
    # Check warmup and decay behavior
    if len(learning_rates) >= 3:
        warmup_complete = learning_rates[1] > learning_rates[0]  # LR should increase during warmup
        decay_active = learning_rates[-1] < learning_rates[1]    # LR should decrease after warmup
    else:
        warmup_complete = True
        decay_active = True
    
    print(f"  Warmup working: {'✅' if warmup_complete else '❌'}")
    print(f"  Cosine decay working: {'✅' if decay_active else '❌'}")
    print()


def test_gradient_clipping():
    """Test gradient clipping functionality"""
    print("🧪 Testing Gradient Clipping...")
    
    # Create a simple model with large gradients
    model = torch.nn.Linear(10, 1)
    
    # Create input that will produce large gradients
    x = torch.randn(5, 10) * 10  # Large input
    y = torch.randn(5, 1) * 100  # Large target
    
    # Forward pass
    output = model(x)
    loss = F.mse_loss(output, y)
    
    # Backward pass
    loss.backward()
    
    # Check gradient norms before clipping
    grad_norm_before = torch.sqrt(sum(p.grad.norm()**2 for p in model.parameters() if p.grad is not None))
    
    # Apply gradient clipping
    clip_grad_norm_(model.parameters(), max_norm=0.5)
    
    # Check gradient norms after clipping
    grad_norm_after = torch.sqrt(sum(p.grad.norm()**2 for p in model.parameters() if p.grad is not None))
    
    print(f"  Gradient norm before clipping: {grad_norm_before.item():.4f}")
    print(f"  Gradient norm after clipping:  {grad_norm_after.item():.4f}")
    print(f"  Clipping effective: {'✅' if grad_norm_after.item() <= 0.5 else '❌'}")
    print()


def test_reward_normalization():
    """Test the loosened reward normalization"""
    print("🧪 Testing Loosened Reward Normalization...")
    
    # Simulate reward statistics
    reward_mean = -3.589
    reward_std = 5.224
    
    # Test various reward values
    test_rewards = [-20.0, -10.0, -3.589, 0.0, 10.0, 20.0]
    
    print("  Raw Reward -> Old Norm -> New Norm")
    for raw_reward in test_rewards:
        # Old normalization (tight clipping)
        old_normalized = (raw_reward - reward_mean) / (reward_std + 1e-6)
        old_clipped = np.clip(old_normalized, -2.0, 2.0)
        
        # New normalization (loose clipping)
        new_normalized = (raw_reward - reward_mean) / (reward_std + 1e-6)
        new_clipped = np.clip(new_normalized, -5.0, 5.0)
        
        print(f"  {raw_reward:6.1f} -> {old_clipped:6.3f} -> {new_clipped:6.3f}")
    
    # Check if new normalization preserves more information
    extreme_reward = 25.0
    old_norm = np.clip((extreme_reward - reward_mean) / (reward_std + 1e-6), -2.0, 2.0)
    new_norm = np.clip((extreme_reward - reward_mean) / (reward_std + 1e-6), -5.0, 5.0)
    
    print(f"  Information preservation: {'✅ Better' if abs(new_norm) > abs(old_norm) else '❌ Same'}")
    print()


def plot_scheduler_comparison():
    """Plot comparison between old and new scheduler"""
    print("📊 Plotting Scheduler Comparison...")
    
    total_steps = 1000
    steps = range(total_steps)
    
    # New LambdaLR schedule
    def lr_lambda(step):
        if step < 1000:
            return step / 1000
        return 0.5 * (1 + math.cos((step - 1000) / (total_steps - 1000) * math.pi))
    
    new_lrs = [1e-4 * lr_lambda(step) for step in steps]
    
    # Simulate old ReduceLROnPlateau (step-wise reductions)
    old_lrs = []
    current_lr = 1e-4
    for step in steps:
        if step in [200, 400, 600, 800]:  # Simulate plateau reductions
            current_lr *= 0.7
        old_lrs.append(current_lr)
    
    # Create comparison plot
    plt.figure(figsize=(12, 6))
    
    plt.subplot(1, 2, 1)
    plt.plot(steps, old_lrs, 'r-', linewidth=2, label='Old: ReduceLROnPlateau')
    plt.plot(steps, new_lrs, 'g-', linewidth=2, label='New: LambdaLR (Warmup + Cosine)')
    plt.xlabel('Training Step')
    plt.ylabel('Learning Rate')
    plt.title('Learning Rate Schedule Comparison')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.yscale('log')
    
    plt.subplot(1, 2, 2)
    # Show the difference in learning rate adaptation
    plt.plot(steps[:100], new_lrs[:100], 'g-', linewidth=3, label='Warmup Phase')
    plt.plot(steps[100:], new_lrs[100:], 'b-', linewidth=3, label='Cosine Decay Phase')
    plt.xlabel('Training Step')
    plt.ylabel('Learning Rate')
    plt.title('New Scheduler: Warmup + Cosine Decay')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('scheduler_comparison.png', dpi=150, bbox_inches='tight')
    plt.close()
    
    print("  📈 Scheduler comparison plot saved as 'scheduler_comparison.png'")


def main():
    print("🚀 Testing All Three Key Improvements")
    print("=" * 50)
    
    # Test all improvements
    test_loss_scaling()
    test_lr_scheduler()
    test_gradient_clipping()
    test_reward_normalization()
    
    # Create visualization
    plot_scheduler_comparison()
    
    print("🎯 Summary of Improvements:")
    print("  ✅ Loss Scaling: Clean separation with conf_scale=0.1, balance_scale=1.0, makespan_scale=1.0")
    print("  ✅ LambdaLR Scheduler: Linear warmup (1000 steps) + cosine decay for smooth convergence")
    print("  ✅ Gradient Clipping: max_norm=0.5 for stable training")
    print("  ✅ Reward Normalization: Loosened from [-2,2] to [-5,5] for better information preservation")
    print("  ✅ Optimizer: lr=1e-4, weight_decay=1e-4 for improved convergence")
    
    print(f"\n🔥 Expected Training Benefits:")
    print(f"  • Better loss balance: Confidence scaled down, balance/makespan preserved")
    print(f"  • Smoother convergence: Warmup prevents early instability, cosine decay for fine-tuning")
    print(f"  • Stable gradients: Clipping prevents explosion while preserving learning signal")
    print(f"  • Richer rewards: Wider normalization range preserves more reward information")
    print(f"  • Consistent learning: Fixed lr/weight_decay instead of command-line variation")


if __name__ == "__main__":
    main()
